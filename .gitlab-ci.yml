image: mobileup:android-jdk17

variables:
  GIT_DEPTH : 99999

  PROJECT_ID : AERAN
  CHANNEL_ID : AerostatChannel
  PROJECT_NAME : Aerostat_Android

before_script:
  - chmod +x ./gradlew

stages:
  - build
  - deploy
  - fail_notification

### Debug pipeline

buildDebug:
  stage: build
  tags:
    - mobileup-android
  script:
    - wget -O build.sh https://ci.getmobileup.com/ci/android/build.sh
    - chmod +x build.sh
    - ./build.sh debug
  artifacts:
    expire_in: 2 hours
    paths:
      - app/build/outputs/apk/debug/app-debug.apk
  only:
    - develop
    - merge_requests

deployDebug:
  stage: deploy
  tags:
    - mobileup-android
  script:
    - wget -O deploy_aws.sh https://ci.getmobileup.com/ci/android/deploy_aws.sh
    - chmod +x deploy_aws.sh
    - ./deploy_aws.sh ${PROJECT_ID} ${CHANNEL_ID} ${PROJECT_NAME} debug
  only:
    - develop

### Release pipeline

buildRelease:
  stage: build
  tags:
    - mobileup-android
  script:
    - wget -O build.sh https://ci.getmobileup.com/ci/android/build.sh
    - chmod +x build.sh
    - ./build.sh release "" aab
  artifacts:
    expire_in: 2 hours
    paths:
      - app/build/outputs/apk/release/app-release.apk
      - app/build/outputs/bundle/release/app-release.aab
  only:
    - master

deployRelease:
  stage: deploy
  tags:
    - mobileup-android
  script:
    - wget -O deploy_aws.sh https://ci.getmobileup.com/ci/android/deploy_aws.sh
    - chmod +x deploy_aws.sh
    - ./deploy_aws.sh ${PROJECT_ID} ${CHANNEL_ID} ${PROJECT_NAME} release "" aab
  only:
    - master

#

failureNotification:
  stage: fail_notification
  tags:
    - mobileup-android
  script:
    - wget -O on_failure.sh https://ci.getmobileup.com/ci/android/on_failure.sh
    - chmod +x on_failure.sh
    - ./on_failure.sh ${PROJECT_ID} ${CHANNEL_ID} ${PROJECT_NAME}
  when: on_failure
  only:
    - develop
    - master