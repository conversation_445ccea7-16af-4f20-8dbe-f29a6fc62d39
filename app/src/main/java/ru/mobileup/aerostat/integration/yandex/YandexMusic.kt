package ru.mobileup.aerostat.integration.yandex

import android.app.SearchManager
import android.content.ComponentName
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import com.github.kittinunf.fuel.core.ResponseDeserializable
import com.github.kittinunf.fuel.coroutines.awaitObjectResponse
import com.github.kittinunf.fuel.httpGet
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import ru.mobileup.aerostat.integration.ThirdpartyMusicService

class YandexMusic : ThirdpartyMusicService {

    override suspend fun trackIntent(artist: String, title: String): Intent {
        val track = trackSearch(query = "$artist - $title").tracks.items.first()

        return Intent().apply {
            action = Intent.ACTION_VIEW
            component = ComponentName("ru.yandex.music", "ru.yandex.music.url.ui.UrlActivity")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
            data =
                Uri.parse("https://music.yandex.ru/album/${track.albums.first().id}/track/${track.id}?play=false")
        }
    }

    override fun searchIntent(artist: String, title: String): Intent {
        return Intent().apply {
            action = MediaStore.INTENT_ACTION_MEDIA_PLAY_FROM_SEARCH
            component = ComponentName("ru.yandex.music", "ru.yandex.music.url.ui.UrlActivity")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
            putExtra(SearchManager.QUERY, "$artist - $title")
        }
    }

    private val json = Json { ignoreUnknownKeys = true }

    private suspend fun trackSearch(query: String): SearchResult {

        return "https://music.yandex.ru/handlers/music-search.jsx".httpGet(
            parameters = listOf(
                "text" to query,
                "type" to "tracks",
                "page" to 0.toString()
            )
        )
            .awaitObjectResponse(deserializable = object : ResponseDeserializable<SearchResult> {
                override fun deserialize(content: String): SearchResult {
                    return json.decodeFromString(
                        SearchResult.serializer(),
                        content
                    )
                }
            })
            .third
    }

    @Serializable
    data class SearchResult(
        val tracks: Tracks
    ) {
        @Serializable
        data class Tracks(
            val items: List<Item>
        ) {
            @Serializable
            data class Item(
                val id: Long,
                val albums: List<Album>
            ) {
                @Serializable
                data class Album(
                    val id: Long,
                    val title: String
                )
            }
        }
    }

}