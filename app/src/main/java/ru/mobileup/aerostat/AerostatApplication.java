package ru.mobileup.aerostat;

import android.content.Context;

import androidx.multidex.MultiDex;
import androidx.room.Room;

import com.google.android.gms.analytics.GoogleAnalytics;

import java.io.File;

import dmdevgo.hunky.core.HunkyApplication;
import ru.mobileup.aerostat.storage.podcast_save_progress.AppDatabase;
import ru.mobileup.aerostat.util.Logger;
import ru.mobileup.aerostat.util.ShowReleaseUtils;

/**
 * Created by terrakok on 15.01.15.
 */
public class AerostatApplication extends HunkyApplication {
    private static final String CUE_DIR = "/cue_files/";

    private static AerostatApplication INSTANCE;

    private AppDatabase appDatabase;
    private String mCueDirPath;

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        INSTANCE = this;

        appDatabase = Room
                .databaseBuilder(this, AppDatabase.class, AppDatabase.DATABASE_NAME)
                .build();

        Logger.setIsEnabled(BuildConfig.DEBUG);
        initGoogleAnalytics();
        ShowReleaseUtils.checkAllShowsDownloadStatus(this);
    }

    public static AerostatApplication getInstance() {
        return INSTANCE;
    }

    public AppDatabase getAppDatabase() {
        return appDatabase;
    }

    public String getCueFilesDirPath() {
        if (mCueDirPath == null) {
            File cueDir = new File(getFilesDir().getAbsoluteFile() + CUE_DIR);
            if (!cueDir.exists()) {
                cueDir.mkdirs();
            }
            mCueDirPath = cueDir.getAbsolutePath();
        }
        return mCueDirPath;
    }

    private void initGoogleAnalytics() {
        GoogleAnalytics analytics = GoogleAnalytics.getInstance(this);

        analytics.setDryRun(BuildConfig.DEBUG); // Prevent sending reports to server while debugging
    }
}
