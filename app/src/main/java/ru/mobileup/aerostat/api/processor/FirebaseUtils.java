package ru.mobileup.aerostat.api.processor;

import ru.mobileup.aerostat.api.model.ShowRelease;

/**
 * Created by terrakok 14.09.15
 */
public class FirebaseUtils {
    public static final String TABLE_NAME = "aerostats";
    public static final String NUMBER = "showNumber";

    public static ShowRelease convertFromFirebase(ShowRelease.FromFirebase fromFirebase) {
        return new ShowRelease(
                fromFirebase.getShowNumber().intValue(),
                fromFirebase.getShowName(),
                fromFirebase.getShowDate(),
                fromFirebase.getShowDescription(),
                fromFirebase.getMusicURL(),
                fromFirebase.getCueURL(),
                false,
                ShowRelease.STATUS_NOT_SAVED,
                0,
                null,
                0
        );
    }
}
