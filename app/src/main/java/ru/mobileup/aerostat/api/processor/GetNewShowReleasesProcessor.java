package ru.mobileup.aerostat.api.processor;

import android.content.ContentValues;
import android.content.Context;

import com.google.firebase.database.DataSnapshot;
import com.google.firebase.database.DatabaseError;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.firebase.database.Query;
import com.google.firebase.database.ValueEventListener;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

import dmdevgo.hunky.core.BaseProcessor;
import dmdevgo.hunky.core.ProcessError;
import dmdevgo.hunky.core.Report;
import ru.mobileup.aerostat.api.model.ShowRelease;
import ru.mobileup.aerostat.storage.AerostatPrefs;
import ru.mobileup.aerostat.storage.provider.AerostatContentProvider;
import ru.mobileup.aerostat.storage.provider.ContentHelper;
import ru.mobileup.aerostat.storage.provider.Contract;
import ru.mobileup.aerostat.util.Logger;
import ru.mobileup.aerostat.util.NetworkUtilsKt;
import ru.mobileup.aerostat.util.ShowReleaseUtils;

/**
 * Created by terrakok on 15.01.15.
 */
public class GetNewShowReleasesProcessor extends BaseProcessor {
    private static final String TAG = "GetNewShowReleasesProcessor";

    private static final int GET_SHOW_RELEASE_TIME_OUT_MILLIS = 60000;

    private final int startReleaseNumber;

    public GetNewShowReleasesProcessor(int startReleaseNumber) {
        Logger.v(TAG, "GetNewShowReleasesProcessor: startReleaseNumber = " + startReleaseNumber);
        this.startReleaseNumber = startReleaseNumber;
    }

    @Override
    public Report execute(Context context, ProgressListener progressListener) {

        Logger.logToCrashlytics("GetNewShowReleasesProcessor started");

        if (!NetworkUtilsKt.isNetworkAvailable(context)) {
            Logger.logToCrashlytics("GetNewShowReleasesProcessor: Network is not available");
            return Report.newErrorReport(ProcessError.newNetworkError("Network is not available", -1));
        }

        final List<ShowRelease> releaseModelList = new ArrayList<>();

        // D>-
        // Firebase doesn't have synchronous call, so we can't use it inside Hunky processor.
        // We can ether use Semaphore to emulate synchronous call, or refactor to use async call and then start Hunky to put result in the db.
        // Decided to go with a Semaphore.
        // http://stackoverflow.com/questions/33203379/setting-singleton-property-value-in-firebase-listener

        DatabaseReference databaseReference = FirebaseDatabase.getInstance().getReference();
        Query queryRef = databaseReference.child(FirebaseUtils.TABLE_NAME)
                .orderByChild(FirebaseUtils.NUMBER)
                .startAt(startReleaseNumber);

        final Semaphore semaphore = new Semaphore(1);

        try {
            semaphore.tryAcquire(GET_SHOW_RELEASE_TIME_OUT_MILLIS, TimeUnit.MILLISECONDS);
            queryRef.addListenerForSingleValueEvent(new ValueEventListener() {
                @Override
                public void onDataChange(DataSnapshot dataSnapshot) {
                    List<ShowRelease> showReleaseList = new ArrayList<>();
                    for (DataSnapshot snapshot : dataSnapshot.getChildren()) {
                        ShowRelease.FromFirebase fromFirebase = snapshot.getValue(ShowRelease.FromFirebase.class);
                        if (fromFirebase != null) {
                            showReleaseList.add(FirebaseUtils.convertFromFirebase(fromFirebase));
                        }
                    }
                    Collections.reverse(showReleaseList);
                    releaseModelList.addAll(showReleaseList);

                    semaphore.release();
                }

                @Override
                public void onCancelled(DatabaseError databaseError) {
                    Logger.logToCrashlytics("Firebase error" + databaseError);
                    semaphore.release();
                }
            });
        } catch (InterruptedException e) {
            Logger.logToCrashlytics("GetNewShowReleasesProcessor timeout 1", e);
            semaphore.release();
            return Report.newErrorReport(ProcessError.newNetworkError("Timeout", -1));
        }

        try {
            semaphore.tryAcquire(GET_SHOW_RELEASE_TIME_OUT_MILLIS, TimeUnit.MILLISECONDS);
            if (releaseModelList.size() > 0) {

                Logger.v(TAG, "Inserting new ShowRelease to provider: " + releaseModelList.size());
                ContentValues[] valuesArr = new ContentValues[releaseModelList.size()];
                for (int j = 0; j < releaseModelList.size(); j++) {
                    valuesArr[j] = ContentHelper.createShowReleaseContentValues(releaseModelList.get(j));
                }

                int numInsert = context.getContentResolver().bulkInsert(Contract.ShowReleaseTable.CONTENT_URI, valuesArr);
                Logger.v(TAG, "Inserting new tasks to provider DONE, count = " + numInsert);

                // Если это была принудительная загрузка всех выпусков (startReleaseNumber == 1),
                // дополнительно обновляем существующие записи
                if (startReleaseNumber == 1) {
                    Logger.v(TAG, "Force reload detected, updating existing shows to preserve user data");

                    // Обновляем файлы для выпусков с учетом изменений URL и статуса скачивания.
                    ShowReleaseUtils.updateChangedFiles(context, releaseModelList);

                    ContentValues[] updateValuesArr = new ContentValues[releaseModelList.size()];
                    for (int j = 0; j < releaseModelList.size(); j++) {
                        updateValuesArr[j] = ContentHelper.createShowReleaseUpdateContentValues(releaseModelList.get(j));
                    }

                    // Используем оптимизированное массовое обновление в одной транзакции
                    int updateCount = AerostatContentProvider.performBulkUpdate(
                            context, Contract.ShowReleaseTable.CONTENT_URI, updateValuesArr
                    );
                    Logger.v(TAG, "Updated existing shows count = " + updateCount);

                    AerostatPrefs.putLastForceReloadTime(context, System.currentTimeMillis());
                    Logger.v(TAG, "Force reload completed, updated last force reload time");
                }

                //сохраним номер последнего выпуска
                Logger.v(TAG, "New last show number = " + (startReleaseNumber + (releaseModelList.size() - 1)));
                AerostatPrefs.putMaxShowNumber(context, startReleaseNumber + (releaseModelList.size() - 1));
            }

            semaphore.release();
            return Report.newSuccessReport(releaseModelList.size());
        } catch (InterruptedException e) {
            Logger.logToCrashlytics("GetNewShowReleasesProcessor timeout 2", e);
            semaphore.release();
            return Report.newErrorReport(ProcessError.newNetworkError("Timeout", -1));
        }
    }

    @Override
    public String getSubTag() {
        return startReleaseNumber + "";
    }
}
