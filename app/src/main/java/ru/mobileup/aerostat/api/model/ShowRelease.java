package ru.mobileup.aerostat.api.model;

import com.google.firebase.database.IgnoreExtraProperties;

import java.io.Serializable;

/**
 * Created by terrakok on 15.01.15.
 */
public class ShowRelease implements Serializable {
    public static final int STATUS_NOT_SAVED = 0;
    public static final int STATUS_SAVED = 1;
    public static final int STATUS_DOWNLOADING = 2;

    private int number;
    private String name;
    private String subTitle;
    private String description;
    private String fileUrl;
    private String cueUrl;
    private boolean favorite;
    private int savedStatus;
    private long downloadingIndex;
    private String savedFilePath;
    private float listeningProgress;

    public ShowRelease(int number, String name, String subTitle, String description, String fileUrl, String cueUrl, boolean favorite, int savedStatus, long downloadingIndex, String savedFilePath, float listeningProgress) {
        this.number = number;
        this.name = name;
        this.subTitle = subTitle;
        this.description = description;
        this.fileUrl = fileUrl;
        this.favorite = favorite;
        this.savedStatus = savedStatus;
        this.downloadingIndex = downloadingIndex;
        this.savedFilePath = savedFilePath;
        this.cueUrl = cueUrl;
        this.listeningProgress = listeningProgress;
    }

    public int getNumber() {
        return number;
    }

    public String getName() {
        return name;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public String getDescription() {
        return description;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public String getCueUrl() {
        return cueUrl;
    }

    public boolean isFavorite() {
        return favorite;
    }

    public void setFavorite(boolean favorite) {
        this.favorite = favorite;
    }

    public int getSavedStatus() {
        return savedStatus;
    }

    public void setSavedStatus(int savedStatus) {
        this.savedStatus = savedStatus;
    }

    public String getSavedFilePath() {
        return savedFilePath;
    }

    public void setSavedFilePath(String savedFilePath) {
        this.savedFilePath = savedFilePath;
    }

    public long getDownloadingIndex() {
        return downloadingIndex;
    }

    public void setDownloadingIndex(long downloadingIndex) {
        this.downloadingIndex = downloadingIndex;
    }

    public float getListeningProgress() {
        return listeningProgress;
    }

    public void setListeningProgress(float listeningProgress) {
        this.listeningProgress = listeningProgress;
    }

    public boolean isListened() {
        return this.listeningProgress >= 0.9;
    }

    @IgnoreExtraProperties
    public static final class FromFirebase {
        private String showName;
        private String showDate;
        private String showDescription;
        private String musicURL;
        private Long showNumber;
        private String cueURL;

        private FromFirebase() {}

        public String getShowName() {
            return showName;
        }

        public String getShowDate() {
            return showDate;
        }

        public String getShowDescription() {
            return showDescription;
        }

        public String getMusicURL() {
            return musicURL;
        }

        public Long getShowNumber() {
            return showNumber;
        }

        public String getCueURL() {
            return cueURL;
        }
    }
}
