package ru.mobileup.aerostat.util;

import android.util.Log;

import com.google.firebase.crashlytics.FirebaseCrashlytics;

/**
 * Created by terrakok on 16.01.15.
 */
public class Logger {

    private static final String TAG = "Aerostat";
    private static volatile boolean sIsEnabled = false;

    private Logger() {}

    public static boolean isEnabled() {
        return sIsEnabled;
    }

    public static void setIsEnabled(boolean isEnabled) {
        sIsEnabled = isEnabled;
    }

    private static String getTag() {
        return TAG;
    }

    private static String getTag(String subTag) {
        return TAG + "/" + subTag;
    }

    public static void v(String message) {
        if (isEnabled()) {
            Log.v(getTag(), message);
        }
    }

    public static void v(String subTag, String message) {
        if (isEnabled()) {
            Log.v(getTag(subTag), message);
        }
    }

    public static void d(String message) {
        if (isEnabled()) {
            Log.d(getTag(), message);
        }
    }

    public static void d(String subTag, String message) {
        if (isEnabled()) {
            Log.d(getTag(subTag), message);
        }
    }

    public static void i(String message) {
        if (isEnabled()) {
            Log.i(getTag(), message);
        }
    }

    public static void i(String subTag, String message) {
        if (isEnabled()) {
            Log.i(getTag(subTag), message);
        }
    }

    public static void w(String message) {
        if (isEnabled()) {
            Log.w(getTag(), message);
        }
    }

    public static void w(String subTag, String message) {
        if (isEnabled()) {
            Log.w(getTag(subTag), message);
        }
    }

    public static void w(String message, Throwable e) {
        if (isEnabled()) {
            Log.w(getTag(), message, e);
        }
    }

    public static void w(String subTag, String message, Throwable e) {
        if (isEnabled()) {
            Log.w(getTag(subTag), message, e);
        }
    }

    public static void e(String message) {
        if (isEnabled()) {
            Log.e(getTag(), message);
        }
    }

    public static void e(String subTag, String message) {
        if (isEnabled()) {
            Log.e(getTag(subTag), message);
        }
    }

    public static void e(String message, Throwable e) {
        if (isEnabled()) {
            Log.w(getTag(), message, e);
        }
    }

    public static void e(String subTag, String message, Throwable e) {
        if (isEnabled()) {
            Log.w(getTag(subTag), message, e);
        }
    }

    public static void logToCrashlytics(String message) {
        w(message);
        FirebaseCrashlytics.getInstance().log(message);
    }

    public static void logToCrashlytics(String message, Throwable e) {
        e(message, e);
        FirebaseCrashlytics.getInstance().log(message);
        FirebaseCrashlytics.getInstance().recordException(e);
    }

    public static void logToCrashlytics(Throwable e) {
        e(e.getMessage(), e);
        FirebaseCrashlytics.getInstance().recordException(e);
    }
}
