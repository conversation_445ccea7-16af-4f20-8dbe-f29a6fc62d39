package ru.mobileup.aerostat.util;

import android.content.Context;
import android.os.Build;

import ru.mobileup.aerostat.player.ExoPlayer;
import ru.mobileup.aerostat.player.MediaPlayer;
import ru.mobileup.aerostat.player.Player;

/**
 * Created by roman on 15.02.17.
 */

public class PlayerFactory {

    public static Player getPlayer(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            return new ExoPlayer(context);
        } else {
            return new MediaPlayer(context);
        }
    }
}
