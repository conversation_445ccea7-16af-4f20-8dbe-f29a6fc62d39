package ru.mobileup.aerostat.util

object Formatter {

    /**
     * format milliseconds to "xx:xx"
     *
     * @param time in milliseconds
     * @return "xx:xx" String
     */
    fun formatSimpleTime(time: Long): String? {
        val minutes = (time / 60000L).toInt()
        val secundes = (time / 1000L).toInt() - minutes * 60
        var result = ""
        if (minutes < 10) result += "0"
        result += "$minutes:"
        if (secundes < 10) result += "0"
        result += secundes
        return result
    }
}