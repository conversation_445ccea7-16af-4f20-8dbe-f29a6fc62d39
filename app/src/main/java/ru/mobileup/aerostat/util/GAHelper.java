package ru.mobileup.aerostat.util;

import android.content.Context;

import com.google.android.gms.analytics.GoogleAnalytics;
import com.google.android.gms.analytics.HitBuilders;
import com.google.android.gms.analytics.Tracker;

import java.util.HashMap;

import ru.mobileup.aerostat.R;

/**
 * Created by terrakok on 26.02.15.
 */
public class GAHelper {
    private static final String TAG = "GAHelper";
    private static GAHelper mInstance;

    synchronized public static GAHelper getInstance() {
        if (mInstance == null) {
            mInstance = new GAHelper();
        }

        return mInstance;
    }

    private GAHelper() {
    }

    //GA region
    private enum GATrackerName {
        APP_TRACKER
    }

    private HashMap<GATrackerName, Tracker> mGATrackers = new HashMap<GATrackerName, Tracker>();
    //GA endregion

    private Tracker getTrackerGA(GATrackerName trackerId, Context context) {
        if (!mGATrackers.containsKey(trackerId)) {
            GoogleAnalytics analytics = GoogleAnalytics.getInstance(context);
            Tracker t = analytics.newTracker(R.xml.ga_tracker);
            mGATrackers.put(trackerId, t);
        }
        return mGATrackers.get(trackerId);
    }

    synchronized public void sendEventGA(String categoryId, String actionId, String labelId, Long value, Context context) {
        Tracker tracker = getTrackerGA(GATrackerName.APP_TRACKER, context);
        HitBuilders.EventBuilder paramsBuider = new HitBuilders.EventBuilder()
                .setCategory(categoryId)
                .setAction(actionId)
                .setLabel(labelId);

        if (value != null) {
            paramsBuider.setValue(value);
        }

        tracker.send(paramsBuider.build());
        Logger.d(TAG, "sendEventGA: " + paramsBuider.build().toString());
    }
}
