package ru.mobileup.aerostat.util;

import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;

/**
 * Created by roman on 18.01.17.
 */

public class AppPackagesUtils {

    public static final String DOWNLOAD_MANAGER_PACKAGE_NAME = "com.android.providers.downloads";

    public static boolean verifyAppPackageEnabled(Context context, String appPackageName) {
        int state = context.getPackageManager().getApplicationEnabledSetting(appPackageName);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            return state != PackageManager.COMPONENT_ENABLED_STATE_DISABLED
                    && state != PackageManager.COMPONENT_ENABLED_STATE_DISABLED_USER
                    && state != PackageManager.COMPONENT_ENABLED_STATE_DISABLED_UNTIL_USED;
        } else {
            return state != PackageManager.COMPONENT_ENABLED_STATE_DISABLED
                    && state != PackageManager.COMPONENT_ENABLED_STATE_DISABLED_USER;
        }
    }

    public static void openAppPackageDetailsSettings(Context context, String appPackageName) {
        try {
            Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.setData(Uri.parse("package:" + appPackageName));
            context.startActivity(intent);
        } catch (ActivityNotFoundException e) {
            // if not found - just show all settings
            Intent intent = new Intent(Settings.ACTION_MANAGE_APPLICATIONS_SETTINGS);
            context.startActivity(intent);
        }
    }
}
