package ru.mobileup.aerostat.util;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.ContentResolver;
import android.content.Context;
import android.content.DialogInterface;
import android.database.Cursor;
import android.net.Uri;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.annotation.StringRes;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import ru.mobileup.aerostat.AerostatApplication;
import ru.mobileup.aerostat.R;
import ru.mobileup.aerostat.api.model.ShowRelease;
import ru.mobileup.aerostat.cue.CueHelper;
import ru.mobileup.aerostat.storage.DownloadManager;
import ru.mobileup.aerostat.storage.podcast_save_progress.PodcastTimeListeningProgress;
import ru.mobileup.aerostat.storage.podcast_save_progress.PodcastTimeListeningProgressDao;
import ru.mobileup.aerostat.storage.provider.ContentHelper;
import ru.mobileup.aerostat.storage.provider.Contract;
import ru.mobileup.aerostat.ui.main.MainActivity;

/**
 * Created by terrakok on 19.03.15.
 */
public class ShowReleaseUtils {

    private static Dialog downloadErrorDialog;

    private static ExecutorService executorService = Executors.newFixedThreadPool(1);   // for frequent DB updates (listening progress, favorite, ect.)

    public static @Nullable
    ShowRelease getShowRelease(int number, Context context) {
        try (Cursor cursor = context.getContentResolver().query(
                Contract.ShowReleaseTable.buildShowUri(number),
                null,
                null,
                null,
                null
        )) {
            if (cursor.moveToFirst()) {
                return Contract.ShowReleaseTable.getShowInfo(cursor);
            } else {
                return null;
            }
        }
    }

    public static void checkAllShowsDownloadStatus(final Context context) {
        new Thread(() -> {
            Cursor cursor = context.getContentResolver().query(
                    Contract.ShowReleaseTable.CONTENT_URI,
                    null,
                    null,
                    null,
                    null
            );

            if (cursor.moveToFirst()) {
                do {
                    ShowRelease showRelease = Contract.ShowReleaseTable.getShowInfo(cursor);
                    DownloadManager.INSTANCE.checkAndFixDownloadStatus(showRelease);
                } while (cursor.moveToNext());
            }
            cursor.close();
        }).start();
    }

    public static void setFavorite(final ShowRelease showRelease, boolean favorite, final Context context) {
        if (showRelease.isFavorite() == favorite) return;

        showRelease.setFavorite(favorite);

        GAHelper.getInstance().sendEventGA(
                context.getString(R.string.ga_category_user_action),
                context.getString(R.string.ga_event_click),
                context.getString(favorite ? R.string.ga_label_add_favorite : R.string.ga_label_remove_favorite),
                Long.getLong(showRelease.getNumber() + ""),
                context
        );

        executorService.submit(() -> {
            context.getContentResolver().update(
                    Contract.ShowReleaseTable.buildShowUri(showRelease.getNumber()),
                    ContentHelper.createFavoriteContentValues(showRelease),
                    null,
                    null
            );
        });
    }

    public static void updateDownloadingState(final ShowRelease showRelease, final Context context) {
        executorService.submit(() -> {
            context.getContentResolver().update(
                    Contract.ShowReleaseTable.buildShowUri(showRelease.getNumber()),
                    ContentHelper.createDownloadingStateValues(showRelease),
                    null,
                    null
            );
        });
    }

    public static void setListeningProgress(final ShowRelease showRelease, float listeningProgress, final Context context) {
        if (showRelease.getListeningProgress() == listeningProgress) return;

        showRelease.setListeningProgress(listeningProgress);

        executorService.submit(() -> {
            context.getContentResolver().update(
                    Contract.ShowReleaseTable.buildShowUri(showRelease.getNumber()),
                    ContentHelper.createListeningProgressContentValues(showRelease),
                    null,
                    null
            );
        });
    }

    public static void setListeningTime(final ShowRelease showRelease, int listeningTime) {
        PodcastTimeListeningProgressDao dao = AerostatApplication.getInstance().getAppDatabase().podcastTimeListeningProgressDao();
        PodcastTimeListeningProgress progress = new PodcastTimeListeningProgress(showRelease.getNumber(), listeningTime);
        executorService.submit(() -> dao.insert(progress));
    }

    public static int getListeningTime(final ShowRelease showRelease) {
        PodcastTimeListeningProgressDao dao = AerostatApplication.getInstance().getAppDatabase().podcastTimeListeningProgressDao();
        try {
            // NOTE: it blocks main thread, but it is not a big problem.
            // ExecutorService is used to prevent "IllegalStateException: Cannot access database on the main thread"
            return executorService.submit(
                    () -> dao.getTimeListeningProgress(showRelease.getNumber())
            ).get();
        } catch (ExecutionException | InterruptedException e) {
            Logger.e("getListeningTime failed", e);
            return 0;
        }
    }

    public static void downloadShowOnDisk(final ShowRelease showRelease, final Activity activity) {
        if (!DownloadManager.INSTANCE.isDownloadDirectoryAvailable()) {
            if(activity instanceof MainActivity) {
                ((MainActivity)activity).forceChooseDownloadDirectory(showRelease);
            }
        } else if (showRelease.getSavedStatus() == ShowRelease.STATUS_NOT_SAVED) {
            GAHelper.getInstance().sendEventGA(
                    activity.getString(R.string.ga_category_user_action),
                    activity.getString(R.string.ga_event_click),
                    activity.getString(R.string.ga_label_save_on_disk),
                    Long.valueOf(showRelease.getNumber() + ""),
                    activity
            );
            DownloadManager.INSTANCE.downloadShowOnDisk(showRelease);
        } else {
            Toast.makeText(activity, activity.getString(R.string.already_downloading), Toast.LENGTH_SHORT).show();
        }
    }

    public static void showDownloadErrorDialog(Context context, @StringRes int errorRes) {
        if (downloadErrorDialog != null && downloadErrorDialog.isShowing()) {
            downloadErrorDialog.dismiss();
        }

        downloadErrorDialog = new AlertDialog.Builder(context)
                .setMessage(errorRes)
                .setPositiveButton(R.string.common_close, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                })
                .show();
    }

    public static void showRemoveDialog(final ShowRelease showRelease, final Context context) {
        String showName = FileUtils.getFileName(context, Uri.parse(showRelease.getSavedFilePath()));
        if(showName == null) {
            showName = Uri.parse(showRelease.getFileUrl()).getLastPathSegment();
        }
        new AlertDialog.Builder(context)
                .setTitle(R.string.delete_show_dialog_title)
                .setMessage(context.getString(R.string.delete_show_dialog_msg, showName))
                .setNegativeButton(R.string.common_cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                })
                .setPositiveButton(R.string.delete_show_dialog_positive_bt, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        removeShowFromDisk(showRelease, context);
                        dialog.dismiss();
                    }
                })
                .create().show();
    }

    private static void removeShowFromDisk(final ShowRelease showRelease, final Context context) {
        if (showRelease.getSavedStatus() == ShowRelease.STATUS_SAVED) {
            GAHelper.getInstance().sendEventGA(
                    context.getString(R.string.ga_category_user_action),
                    context.getString(R.string.ga_event_click),
                    context.getString(R.string.ga_label_remove_from_disk),
                    Long.getLong(showRelease.getNumber() + ""),
                    context
            );

            CueHelper.deleteCueFile(showRelease);
            DownloadManager.INSTANCE.removeShow(showRelease);
        }
    }


    /**
     * Make dialog if user want cancel loading
     */
    public static void cancelLoadingDialog(final ShowRelease showRelease, final Context context) {
        new AlertDialog.Builder(context)
                .setTitle(R.string.cancel_loading_show_dialog_title)
                .setMessage(context.getString(R.string.cancel_loading_show_dialog_msg, Uri.parse(Uri.decode(showRelease.getSavedFilePath())).getLastPathSegment()))
                .setNegativeButton(R.string.cancel_loading_show_dialog_negative_bt, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                })
                .setPositiveButton(R.string.cancel_loading_show_dialog_positive_bt, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        cancelLoadingProgress(showRelease, context);
                        dialog.dismiss();
                    }
                })
                .create().show();
    }

    /**
     * cancel loading progress
     */
    private static void cancelLoadingProgress(final ShowRelease showRelease, final Context context) {
        if (showRelease.getSavedStatus() == ShowRelease.STATUS_DOWNLOADING) {
            GAHelper.getInstance().sendEventGA(
                    context.getString(R.string.ga_category_user_action),
                    context.getString(R.string.ga_event_click),
                    context.getString(R.string.ga_label_cancel_loading),
                    Long.getLong(showRelease.getNumber() + ""),
                    context
            );

            DownloadManager.INSTANCE.cancelDownload(showRelease);
        }
    }

    /**
     * Получает текущую информацию о файлах для всех выпусков из БД.
     * Возвращает Map где ключ - номер выпуска, значение - ShowFileInfo с информацией о файлах.
     */
    private static Map<Integer, ShowFileInfo> getCurrentFileInfos(Context context) {
        Map<Integer, ShowFileInfo> result = new HashMap<>();

        String[] projection = {
                Contract.ShowReleaseTable.SHOW_NUMBER,
                Contract.ShowReleaseTable.SHOW_CUE_URL,
                Contract.ShowReleaseTable.SHOW_FILE_URL,
                Contract.ShowReleaseTable.SHOW_SAVED_STATUS
        };

        ContentResolver resolver = context.getContentResolver();
        try (Cursor cursor = resolver.query(
                Contract.ShowReleaseTable.CONTENT_URI,
                projection,
                null,
                null,
                null
        )) {
            if (cursor != null && cursor.moveToFirst()) {
                int numberColumnIndex = cursor.getColumnIndex(Contract.ShowReleaseTable.SHOW_NUMBER);
                int cueUrlColumnIndex = cursor.getColumnIndex(Contract.ShowReleaseTable.SHOW_CUE_URL);
                int fileUrlColumnIndex = cursor.getColumnIndex(Contract.ShowReleaseTable.SHOW_FILE_URL);
                int savedStatusColumnIndex = cursor.getColumnIndex(Contract.ShowReleaseTable.SHOW_SAVED_STATUS);

                do {
                    int showNumber = cursor.getInt(numberColumnIndex);
                    String cueUrl = cursor.getString(cueUrlColumnIndex);
                    String fileUrl = cursor.getString(fileUrlColumnIndex);
                    int savedStatus = cursor.getInt(savedStatusColumnIndex);

                    ShowFileInfo fileInfo = new ShowFileInfo(cueUrl, fileUrl, savedStatus);
                    result.put(showNumber, fileInfo);
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            Logger.e("ShowReleaseUtils", "Failed to get current file infos", e);
        }

        return result;
    }

    /**
     * Обновляет файлы для выпусков с учетом изменений URL и статуса скачивания.
     * Логика:
     * 1) Если файл в процессе скачивания (STATUS_DOWNLOADING) и поменялся fileUrl или cueUrl,
     *    то нужно удалить эти файлы и запустить скачивание снова
     * 2) Если файл скачан (STATUS_SAVED), то не нужно ничего делать с файлами, даже если урлы поменялись.
     * 3) Если файл не скачан и поменялся cueUrl, то удалить cue-файл.
     */
    public static void updateChangedFiles(Context context, List<ShowRelease> newShows) {
        if (newShows == null || newShows.isEmpty()) {
            return;
        }

        try {
            // Получаем текущую информацию о файлах из БД
            Map<Integer, ShowFileInfo> currentFileInfos = getCurrentFileInfos(context);

            int processedCount = 0;
            for (ShowRelease newShow : newShows) {
                int showNumber = newShow.getNumber();
                String newCueUrl = newShow.getCueUrl();
                String newFileUrl = newShow.getFileUrl();
                ShowFileInfo currentFileInfo = currentFileInfos.get(showNumber);

                // Если выпуск новый, пропускаем
                if (currentFileInfo == null) {
                    continue;
                }

                String currentCueUrl = currentFileInfo.getCueUrl();
                String currentFileUrl = currentFileInfo.getFileUrl();
                int currentSavedStatus = currentFileInfo.getSavedStatus();

                boolean cueUrlChanged = !Objects.equals(currentCueUrl, newCueUrl);
                boolean fileUrlChanged = !Objects.equals(currentFileUrl, newFileUrl);

                if (currentSavedStatus == ShowRelease.STATUS_DOWNLOADING) {
                    // 1) Файл в процессе скачивания и поменялся fileUrl или cueUrl
                    if (fileUrlChanged || cueUrlChanged) {
                        Logger.v("ShowReleaseUtils", "Show " + showNumber + " is downloading and URLs changed. " +
                                "FileUrl: " + currentFileUrl + " -> " + newFileUrl + ", " +
                                "CueUrl: " + currentCueUrl + " -> " + newCueUrl + ". " +
                                "Need to restart download.");

                        ShowRelease currentShowRelease = getShowRelease(showNumber, context);
                        if (currentShowRelease != null) {
                            DownloadManager.INSTANCE.cancelDownload(currentShowRelease);
                            CueHelper.deleteCueFile(currentShowRelease);
                            DownloadManager.INSTANCE.removeShow(currentShowRelease);
                            DownloadManager.INSTANCE.downloadShowOnDisk(newShow);
                            processedCount++;
                        }
                    }
                } else if (currentSavedStatus == ShowRelease.STATUS_SAVED) {
                    // 2) Файл скачан - ничего не делаем, даже если урлы поменялись
                    if (fileUrlChanged || cueUrlChanged) {
                        Logger.v("ShowReleaseUtils", "Show " + showNumber + " is saved and URLs changed, but keeping files as is.");
                    }
                } else if (currentSavedStatus == ShowRelease.STATUS_NOT_SAVED) {
                    // 3) Файл не скачан и поменялся cueUrl - удаляем cue-файл
                    if (cueUrlChanged) {
                        Logger.v("ShowReleaseUtils", "Show " + showNumber + " is not saved and cueUrl changed: " +
                                currentCueUrl + " -> " + newCueUrl + ". Deleting cue file.");
                        CueHelper.deleteCueFile(newShow);
                        processedCount++;
                    }
                }
            }

            if (processedCount > 0) {
                Logger.v("ShowReleaseUtils", "Processed " + processedCount + " shows with changed files");
            }
        } catch (Exception e) {
            Logger.e("ShowReleaseUtils", "Failed to update changed files", e);
        }
    }
}

/**
 * Класс для хранения информации о файлах выпуска
 */
class ShowFileInfo {
    private final String cueUrl;
    private final String fileUrl;
    private final int savedStatus;

    public ShowFileInfo(String cueUrl, String fileUrl, int savedStatus) {
        this.cueUrl = cueUrl;
        this.fileUrl = fileUrl;
        this.savedStatus = savedStatus;
    }

    public String getCueUrl() {
        return cueUrl;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public int getSavedStatus() {
        return savedStatus;
    }
}