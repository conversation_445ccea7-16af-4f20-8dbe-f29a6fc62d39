package ru.mobileup.aerostat.util;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.net.Uri;
import android.provider.OpenableColumns;

import androidx.annotation.Nullable;
import androidx.documentfile.provider.DocumentFile;

import java.io.File;
import java.util.List;

public final class FileUtils {

    public static boolean isTreeUri(Uri uri) {
        final List<String> paths = uri.getPathSegments();
        return (paths.size() >= 2 && "tree".equals(paths.get(0)));
    }

    @Nullable
    public static DocumentFile uriToDocumentFile(Uri uri, Context context) {
        DocumentFile documentFile = DocumentFile.fromSingleUri(context, uri);

        if (documentFile != null && documentFile.isDirectory() && isTreeUri(uri)) {
            return DocumentFile.fromTreeUri(context, uri);
        } else if (DocumentFile.isDocumentUri(context, uri)) {
            return DocumentFile.fromSingleUri(context, uri);
        } else {
            return DocumentFile.fromFile(new File(uri.getPath()));
        }
    }

    @Nullable
    public static DocumentFile uriToTreeDocumentFile(Uri uri, Context context) {
        if (isTreeUri(uri)) {
            return DocumentFile.fromTreeUri(context, uri);
        } else {
            File file = new File(uri.getPath());

            if (file.exists() || file.mkdirs()) {
                return DocumentFile.fromFile(file);
            } else {
                return null;
            }
        }
    }

    public static boolean canRead(Uri uri, Context context) {
        return context.checkCallingOrSelfUriPermission(uri, Intent.FLAG_GRANT_READ_URI_PERMISSION) == PackageManager.PERMISSION_GRANTED;
    }

    public static boolean canWrite(Uri uri, Context context) {
        return context.checkCallingOrSelfUriPermission(uri, Intent.FLAG_GRANT_WRITE_URI_PERMISSION) == PackageManager.PERMISSION_GRANTED;
    }

    public static boolean canReadAndWrite(Uri uri, Context context) {
        return canRead(uri, context) && canWrite(uri, context);
    }

    @Nullable
    public static String getFileName(Context context, Uri uri) {
        if ("file".equals(uri.getScheme())) {
            return uri.getLastPathSegment();
        } else {
            try {
                String[] projection = new String[]{OpenableColumns.DISPLAY_NAME};
                try (Cursor cursor = context.getContentResolver().query(uri, projection, null, null, null)) {
                    if (cursor != null && cursor.moveToFirst()) {
                        return cursor.getString(0);
                    } else {
                        return null;
                    }
                }
            } catch (Exception e) {
                return null;
            }
        }
    }
}
