package ru.mobileup.aerostat.cue;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import ru.mobileup.aerostat.AerostatApplication;
import ru.mobileup.aerostat.api.model.CueTrackData;
import ru.mobileup.aerostat.api.model.ShowRelease;

/**
 * Created by terrakok 15.12.15
 */
public class CueHelper {
    private static final String DEFAULT_YEAR = "1970-01-01 -0000 ";
    private static final DateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd Z mm:ss:S");
    private static final Pattern TRACK_PATTERN = Pattern.compile("\\s{2}TRACK\\s\\d+\\sAUDIO");
    private static final Pattern TITLE_PATTERN = Pattern.compile("TITLE\\s\"(.*)\"");
    private static final Pattern PERFORMER_PATTERN = Pattern.compile("PERFORMER\\s\"(.*)\"");
    private static final Pattern INDEX_PATTERN = Pattern.compile("INDEX\\s\\d+\\s(.*)");
    private static final String DEFAULT_CUE_FILE_CHARSET = "Windows-1251";

    public static void downloadCueDataIfNeeded(ShowRelease showRelease) throws Exception {
        File newCueFile = getCueFileForShowRelease(showRelease);
        if (!newCueFile.exists()) {
            downloadCueFile(showRelease);
        }
    }

    public static List<CueTrackData> getCueData(ShowRelease showRelease) throws Exception {
        File cueFile = getCueFileForShowRelease(showRelease);
        if (!cueFile.exists()) {
            cueFile = downloadCueFile(showRelease);
        }
        return convertCueFile(cueFile);
    }

    private static File downloadCueFile(ShowRelease showRelease) throws Exception {
        File newCueFile = getCueFileForShowRelease(showRelease);
        if (newCueFile.exists()) {
            newCueFile.delete();
        }

        InputStream input = null;
        OutputStream output = null;
        try {
            URL url = new URL(showRelease.getCueUrl());
            URLConnection connection = url.openConnection();
            connection.connect();

            input = new BufferedInputStream(connection.getInputStream());
            output = new FileOutputStream(newCueFile);

            byte data[] = new byte[1024];
            int count;
            while ((count = input.read(data)) != -1) {
                output.write(data, 0, count);
            }
        } finally {
            if (output != null) {
                output.flush();
                output.close();
            }
            if (input != null) {
                input.close();
            }
        }

        return newCueFile;
    }

    public static void deleteCueFile(ShowRelease showRelease) {
        File newCueFile = getCueFileForShowRelease(showRelease);
        if (newCueFile.exists()) {
            newCueFile.delete();
        }
    }

    private static File getCueFileForShowRelease(ShowRelease showRelease) {
        return new File(AerostatApplication.getInstance().getCueFilesDirPath(), showRelease.getNumber() + ".cue");
    }

    private static List<CueTrackData> convertCueFile(File cueFile) throws Exception {
        List<CueTrackData> result = new ArrayList();
        if (cueFile != null && cueFile.exists()) {
            String cueData = getStringFromFile(cueFile);

            CueTrackData previousTrackData = null;
            for (String trackData : TRACK_PATTERN.split(cueData)) {
                Matcher titleMatcher = TITLE_PATTERN.matcher(trackData);
                Matcher performerMatcher = PERFORMER_PATTERN.matcher(trackData);
                Matcher indexMatcher = INDEX_PATTERN.matcher(trackData);
                if (titleMatcher.find() && performerMatcher.find() && indexMatcher.find()) {
                    long startTime = DATE_FORMAT.parse(DEFAULT_YEAR + indexMatcher.group(1)).getTime();

                    if (previousTrackData != null) {
                        previousTrackData.setStopTime(startTime);
                    }

                    previousTrackData = new CueTrackData(
                            titleMatcher.group(1),
                            performerMatcher.group(1),
                            startTime,
                            Long.MAX_VALUE
                    );
                    result.add(previousTrackData);
                }
            }
        }
        Collections.sort(result, new Comparator<CueTrackData>() {
            @Override
            public int compare(CueTrackData lhs, CueTrackData rhs) {
                return lhs.getStartTime() < rhs.getStartTime() ? 1 : -1;
            }
        });

        return result;
    }

    private static String getStringFromFile(File file) throws Exception {
        StringBuilder sb = new StringBuilder();

        FileInputStream fin = null;
        BufferedReader reader = null;
        try {
            fin = new FileInputStream(file);
            reader = new BufferedReader(new InputStreamReader(fin, DEFAULT_CUE_FILE_CHARSET));
            String line = null;
            while ((line = reader.readLine()) != null) {
                sb.append(line).append("\n");
            }
        } finally {
            if (reader != null) reader.close();
            if (fin != null) fin.close();
        }

        return sb.toString();
    }
}
