package ru.mobileup.aerostat.player;

import android.net.Uri;

/**
 * Created by roman on 15.02.17.
 */

public interface Player {

    PlayerState getCurrentState();

    void prepare(Uri uri);

    void play();

    void pause();

    boolean isPlaying();

    void seekTo(long timeMs);

    long getCurrentPosition();

    long getDuration();

    void setVolume(float audioVolume);

    void release();

    void setListener(PlayerCallback listener);

    boolean isReleased();

    interface PlayerCallback {
        void onCompletion();

        void onPlaying();

        void onPaused();

        void onPreparing();

        void onPrepared();

        void onError(String error);
    }
}
