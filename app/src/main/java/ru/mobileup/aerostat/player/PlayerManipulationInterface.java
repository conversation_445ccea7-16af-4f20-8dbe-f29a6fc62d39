package ru.mobileup.aerostat.player;

import ru.mobileup.aerostat.api.model.ShowRelease;

/**
 * Created by terrakok on 17.03.15.
 */
public interface PlayerManipulationInterface {
    void setListener(PodcastPlayerService.PodcastPlayerCallback listener);

    void openShowRelease(ShowRelease showRelease, boolean shouldStartWhenReady, boolean playOnline);

    void pauseOrResumePlayer(ShowRelease showRelease);

    void discreteForward();

    void discreteRewind();

    void setProgress(int timeMS);

    PlayerState getCurrentState();
}
