package ru.mobileup.aerostat.player;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.bluetooth.BluetoothHeadset;
import android.content.AsyncQueryHandler;
import android.content.BroadcastReceiver;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.Cursor;
import android.media.AudioManager;
import android.media.session.MediaSession;
import android.net.Uri;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.view.KeyEvent;
import android.widget.RemoteViews;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import androidx.documentfile.provider.DocumentFile;

import java.lang.ref.WeakReference;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import ru.mobileup.aerostat.R;
import ru.mobileup.aerostat.api.model.CueTrackData;
import ru.mobileup.aerostat.api.model.ShowRelease;
import ru.mobileup.aerostat.cue.CueHelper;
import ru.mobileup.aerostat.storage.AerostatPrefs;
import ru.mobileup.aerostat.storage.provider.Contract;
import ru.mobileup.aerostat.ui.main.MainActivity;
import ru.mobileup.aerostat.util.FileUtils;
import ru.mobileup.aerostat.util.Logger;
import ru.mobileup.aerostat.util.PlayerFactory;
import ru.mobileup.aerostat.util.ShowReleaseUtils;

/**
 * Created by terrakok on 19.01.15.
 */
public class PodcastPlayerService extends Service implements
        PlayerManipulationInterface,
        AudioManager.OnAudioFocusChangeListener,
        Player.PlayerCallback {

    private static final String TAG = "PlayerService";
    private static final int NOTIFICATION_ID = 242;
    private static final String NOTIFICATION_CHANNEL_ID = "Aerostat Player";

    private static final String ACTION_TAG = "podcast.player.";
    private static final String MANIPULATION_ACTION_DISCRETE_FORWARD = ACTION_TAG + "ma_forward";
    private static final String MANIPULATION_ACTION_DISCRETE_REWIND = ACTION_TAG + "ma_rewind";
    private static final String MANIPULATION_ACTION_PLAY_PAUSE = ACTION_TAG + "ma_pause_play";
    private static final String MANIPULATION_ACTION_CANCEL = ACTION_TAG + "ma_cancel";

    private static final int QUERY_LAST_PLAYED_TOKEN = 100;
    private static final int QUERY_NEXT_SHOW_TOKEN = 200;
    private static final int QUERY_PREVIOUS_SHOW_TOKEN = 300;

    private static final int DISCRETE_STEP_MS = 15000;

    private static final float VOLUME_DUCK = .5f;
    private static final float VOLUME_NORMAL = 1;

    private PodcastPlayerCallback mListener;

    private AudioManager mAudioManager;
    private MediaSession mMediaSession;

    IntentFilter mHeadsetReceiverFilter;
    HeadsetBroadCastReceiver mHeadsetReceiver;

    // флаг захвата вокуса
    private boolean isAudioFocusGranted = false;

    // послденее состояние фокуса
    private int mLastKnownAudioFocusState;

    //флаг для остановки потока, который следит за прогрессом воспроизведения
    private boolean mUpdateProgress = false;

    //хендлер для запуска потока "прогресса воспроизведения"
    private Handler mProgressHandler;

    //хендлер для загрузки и парсинга cue файлов
    private Handler mCueFileHandler;
    private List<CueTrackData> mCueTrackDataArray;
    private CueTrackData mCurrentTrackData;
    private Executor mExecutor = Executors.newCachedThreadPool();

    private boolean isBinded;

    private ShowRelease mShow;
    private boolean mShouldStartWhenReady;
    private long mStartPlayTime;

    private ShowReleaseAsyncQueryHandler mQueryHandler;

    private IBinder mBinder = new LocalBinder();

    private Player player;

    // region PlayerManipulationInterface
    public void setListener(PodcastPlayerCallback listener) {
        this.mListener = listener;

        if (mListener != null) {
            if (!player.isReleased()) {
                //если уже что-то играет, то отправляем слушателю инфу.
                startProgressSender();
                if (player.isPlaying()) {
                    Logger.d(TAG, "MediaPlayer playing");
                    mListener.onPlayerStart(mShow);
                } else {
                    Logger.d(TAG, "MediaPlayer on pause");
                    mListener.onPlayerPause(mShow);
                }
            } else {
                // Ничего не воспроизводится
                AerostatPrefs.LastPlayed lastPlayed = AerostatPrefs.getLastPlayed(this, null);
                if (lastPlayed == null) {
                    // Ничего не играет и нечего восстанавливать
                    mListener.onPlayerRelease();
                } else {
                    // Нужно восстановить последний
                    restoreLastPlayedShow(lastPlayed);
                }
            }
        }
    }

    private static void start(Context context) {
        Intent intent = new Intent(context, PodcastPlayerService.class);
        context.startService(intent);
    }

    //region Service Lifecycle
    @Override
    public void onCreate() {
        Logger.v(TAG, "PlayerService created");

        mProgressHandler = new Handler();
        mCueFileHandler = new Handler();
        mAudioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);

        mMediaSession = new MediaSession(getApplicationContext(), TAG);
        mMediaSession.setCallback(new MediaSession.Callback() {
            @Override
            public boolean onMediaButtonEvent(final Intent mediaButtonIntent) {
                if (Intent.ACTION_MEDIA_BUTTON.equals(mediaButtonIntent.getAction())) {
                    KeyEvent event = mediaButtonIntent.getParcelableExtra(Intent.EXTRA_KEY_EVENT);
                    if(event != null) {
                        PodcastPlayerService.this.onMediaButtonEvent(event);
                    }
                }
                return true;
            }
        });

        mHeadsetReceiverFilter = new IntentFilter();
        mHeadsetReceiverFilter.addAction(Intent.ACTION_HEADSET_PLUG);
        mHeadsetReceiverFilter.addAction(BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED);
        mHeadsetReceiver = new HeadsetBroadCastReceiver();
        registerReceiver(mHeadsetReceiver, mHeadsetReceiverFilter);

        player = PlayerFactory.getPlayer(this);
        player.setListener(this);
    }

    @Override
    public IBinder onBind(Intent intent) {
        Logger.v(TAG, "PlayerService binded");

        PodcastPlayerService.start(this); // Starting service on every bind
        isBinded = true;
        return mBinder;
    }

    @Override
    public void onRebind(Intent intent) {
        super.onRebind(intent);
        Logger.v(TAG, "PlayerService onRebind");

        PodcastPlayerService.start(this); // Starting service on every bind
        isBinded = true;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Logger.v(TAG, "PlayerService onStartCommand");

        if (intent != null && intent.getAction() != null) {
            switch (intent.getAction()) {
                case MANIPULATION_ACTION_PLAY_PAUSE:
                    Logger.v(TAG, "MANIPULATION_ACTION_PLAY_PAUSE");
                    if (mShow != null) {
                        pauseOrResumePlayer(mShow);
                    }
                    break;
                case MANIPULATION_ACTION_DISCRETE_FORWARD:
                    Logger.v(TAG, "MANIPULATION_ACTION_DISCRETE_FORWARD");
                    discreteForward();
                    break;
                case MANIPULATION_ACTION_DISCRETE_REWIND:
                    Logger.v(TAG, "MANIPULATION_ACTION_DISCRETE_REWIND");
                    discreteRewind();
                    break;
                case MANIPULATION_ACTION_CANCEL:
                    Logger.v(TAG, "MANIPULATION_ACTION_CANCEL");
                    player.pause();
                    hideNotification();
                    if (!isBinded) stopSelf();
                    break;
            }
        }

        return START_NOT_STICKY;
    }

    private void onMediaButtonEvent(KeyEvent event) {
        Logger.v(TAG, "onMediaButtonEvent " + event);
        if (event.getAction() == KeyEvent.ACTION_UP) {
            switch (event.getKeyCode()) {
                case KeyEvent.KEYCODE_MEDIA_NEXT:
                    if (mShow != null) {
                        takeNextShow(mShow);
                    }
                    break;
                case KeyEvent.KEYCODE_MEDIA_PREVIOUS:
                    if (mShow != null) {
                        takePreviousShow(mShow);
                    }
                    break;
                case KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE:
                case KeyEvent.KEYCODE_HEADSETHOOK:
                    if(mShow != null) {
                        pauseOrResumePlayer(mShow);
                    }
                    break;
                case KeyEvent.KEYCODE_MEDIA_PLAY:
                    if (mShow != null) {
                        play(mShow);
                    }
                    break;
                case KeyEvent.KEYCODE_MEDIA_PAUSE:
                    pause();
                    break;
            }
        }
    }

    @Override
    public boolean onUnbind(Intent intent) {
        Logger.v(TAG, "PlayerService unbinded");
        //stopProgressSender();

        //чтобы при закрытии активити, точно не закрыть плеер
        if (player.isPlaying()) {
            Logger.v(TAG, "restart PlayerService");
            start(this);
        }

        isBinded = false;
        return true;
    }

    @Override
    public void onDestroy() {
        Logger.v(TAG, "PlayerService onDestroy");
        unregisterReceiver(mHeadsetReceiver);
        mMediaSession.release();
        hideNotification();
        abandonAudioFocus();
        player.release();
        mProgressHandler.removeCallbacksAndMessages(null);
        mCueFileHandler.removeCallbacksAndMessages(null);
    }
    //endregion

    // region воспроизвести выпуск
    public void openShowRelease(ShowRelease showRelease, boolean shouldStartWhenReady, boolean playOnline) {
        if (showRelease != null) {
            Logger.v(TAG, "openShowRelease = " + showRelease.getNumber());

            mShow = showRelease;
            mStartPlayTime = showRelease.isListened() ? 0 : ShowReleaseUtils.getListeningTime(showRelease);
            mShouldStartWhenReady = shouldStartWhenReady;
            startCueFileProcess(mShow);

            if (showRelease.getSavedStatus() == ShowRelease.STATUS_SAVED) {
                if (playOnline) {
                    player.release();
                    player.prepare(Uri.parse(showRelease.getFileUrl()));
                } else {
                    DocumentFile documentFile = FileUtils.uriToDocumentFile(Uri.parse(showRelease.getSavedFilePath()), this);
                    if (documentFile.exists()) {
                        player.release();
                        player.prepare(documentFile.getUri());
                    } else if (mListener != null) {
                        Logger.v(TAG, "playFile error! File not exist: " + documentFile.getName());
                        mListener.onPlayerFileNotExist(showRelease);
                    }
                }
            } else {
                player.release();
                player.prepare(Uri.parse(showRelease.getFileUrl()));
            }
        }
    }

    //пауза-продолжение воспроизвеления
    public void pauseOrResumePlayer(ShowRelease showRelease) {
        if (!player.isReleased()) {
            if (player.isPlaying()) {
                player.pause();
            } else {
                player.play();
            }
        } else {
            openShowRelease(showRelease, true, false);
        }
    }

    public void play(ShowRelease showRelease) {
        if (!player.isReleased() && !player.isPlaying()) {
            player.play();
        } else {
            openShowRelease(showRelease, true, false);
        }
    }

    public void pause() {
        if (!player.isReleased() && player.isPlaying()) {
            player.pause();
        }
    }

    private void handlePause() {
        Logger.v(TAG, "PAUSE");

        if (mLastKnownAudioFocusState != AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
            mShouldStartWhenReady = false;
        }

        if (mListener != null) {
            mListener.onPlayerPause(mShow);
        }
        showNotification(false);
        stopProgressSender();

        saveLastPlayed(mShow);
    }

    private void handleStart() {
        Logger.v(TAG, "RESUME");

        requestAudioFocusIfNeeded();

        mShouldStartWhenReady = true;

        if (mListener != null) {
            mListener.onPlayerStart(mShow);
        }

        showNotification(true);
        startProgressSender();
    }

    private void handleStop() {
        Logger.v(TAG, "STOP");
        saveLastPlayed(null);

        //выпуск закончился
        if (mListener != null) {
            mListener.onPlayerStop(mShow);
        }

        takeNextShow(mShow);
    }

    private void handleError(String error) {
        Logger.d(TAG, "onPlayerError error: " + error);
        saveLastPlayed(mShow);

        if (mListener != null) {
            mListener.onPlayerError(mShow);
        }

        showNotification(false);

        abandonAudioFocus();
        player.release();
        if (!isBinded) {
            Logger.v(TAG, "PlayerService [stopSelf]");
            //stopSelf();
        }
    }

    @Override
    public PlayerState getCurrentState() {
        return player.getCurrentState();
    }

    public void discreteForward() {
        Logger.i(TAG, "discreteForward");
        long newTime = player.getCurrentPosition() + DISCRETE_STEP_MS;
        player.seekTo(newTime);
        updateProgressWithTrackData();
    }

    public void discreteRewind() {
        Logger.i(TAG, "discreteRewind");
        long newTime = player.getCurrentPosition() - DISCRETE_STEP_MS;

        if (newTime > 0) {
            player.seekTo(newTime);
        } else {
            player.seekTo(0);
        }
        updateProgressWithTrackData();
    }

    public void setProgress(int timeMS) {
        Logger.i(TAG, "setProgress: " + timeMS);
        player.seekTo(timeMS);
        updateProgressWithTrackData();
    }
    // endregion

    // region focus
    @Override
    public void onAudioFocusChange(int focusChange) {
        mLastKnownAudioFocusState = focusChange;
        switch (focusChange) {
            case AudioManager.AUDIOFOCUS_LOSS:
                Logger.i(TAG, "Focus loss: " + focusChange);

                abandonAudioFocus();
                player.pause();

                break;
            case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT:
                Logger.i(TAG, "Focus loss: " + focusChange);
                player.pause();
                break;
            case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK:
                Logger.i(TAG, "Focus loss: " + focusChange);
                player.setVolume(VOLUME_DUCK);
                break;
            case AudioManager.AUDIOFOCUS_GAIN:
                Logger.i(TAG, "Focus gain: " + focusChange);
                if (mShouldStartWhenReady) player.play();
                player.setVolume(VOLUME_NORMAL);
                break;
        }
    }

    private void requestAudioFocusIfNeeded() {

        if (isAudioFocusGranted) return;

        int result = mAudioManager.requestAudioFocus(this,
                AudioManager.STREAM_MUSIC,
                AudioManager.AUDIOFOCUS_GAIN);

        if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
            Logger.d(TAG, "requestAudioFocusIfNeeded granted");
            mMediaSession.setActive(true);
            isAudioFocusGranted = true;
        } else if (result == AudioManager.AUDIOFOCUS_REQUEST_FAILED) {
            Logger.d(TAG, "said failed");
            mMediaSession.setActive(false);
            isAudioFocusGranted = false;
        }
    }

    private void abandonAudioFocus() {
        if (mAudioManager != null) {
            mAudioManager.abandonAudioFocus(this);
        }
        mMediaSession.setActive(false);
        isAudioFocusGranted = false;
    }
    // endregion

    private void startProgressSender() {
        Logger.i(TAG, "startProgressSender");
        mUpdateProgress = true;
        mProgressHandler.post(mProgressUpdateRunnable);
    }

    private void stopProgressSender() {
        Logger.i(TAG, "stopProgressSender");
        mUpdateProgress = false;
    }

    //самоперезапускающийся отправщик прогресса проигрывания
    private final Runnable mProgressUpdateRunnable = new Runnable() {
        @Override
        public void run() {
            if (mUpdateProgress) {
                updateProgressWithTrackData();
                mProgressHandler.postDelayed(mProgressUpdateRunnable, 500);
            }
        }
    };

    private void updateProgressWithTrackData() {
        if (mListener != null) {
            long position = player.getCurrentPosition();
            mListener.onPlayerProgress(mShow, (int) position, (int) player.getDuration(), getCueData((int) position));
        }
    }

    private void showNotification(boolean isPlaying) {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel notificationChannel = new NotificationChannel(
                    NOTIFICATION_CHANNEL_ID,
                    getString(R.string.notification_player_channel),
                    NotificationManager.IMPORTANCE_DEFAULT
            );

            notificationChannel.enableVibration(false);
            notificationChannel.setSound(null, null);

            NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(notificationChannel);
            }
        }

        safeStartForeground(NOTIFICATION_ID, createNotification(isPlaying, mCurrentTrackData));
    }

    private void hideNotification() {
        stopForeground(true);
    }

    private void updateNotification(CueTrackData trackData) {
        safeStartForeground(NOTIFICATION_ID, createNotification(player.isPlaying(), trackData));
    }

    // region notification
    private Notification createNotification(boolean isPlaying, CueTrackData trackData) {
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        int flagsPI = Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ?  PendingIntent.FLAG_IMMUTABLE : 0;
        PendingIntent contentPI = PendingIntent.getActivity(this, 0, intent, flagsPI);

        // Настраиваем действия кнопок
        PendingIntent pendingPausePlayIntent = PendingIntent.getService(this, 0, getPlayPauseIntent(this), flagsPI);
        PendingIntent pendingForwardIntent = PendingIntent.getService(this, 0, getForwardIntent(this), flagsPI);
        PendingIntent pendingRewindIntent = PendingIntent.getService(this, 0, getRewindIntent(this), flagsPI);
        PendingIntent pendingCancelIntent = PendingIntent.getService(this, 0, getCancelIntent(this), flagsPI); // Кнопка закрыть

        // Готовим иконки
        int playPauseIconRes = isPlaying ? R.drawable.ic_notif_pause_bars : R.drawable.ic_notif_play_arrow;
        int playPauseLargeIconRes = isPlaying ? R.drawable.ic_notif_pause_large : R.drawable.ic_notif_play_large;
        int smallIconDrawableRes = isPlaying ? R.drawable.ic_notification_play : R.drawable.ic_notification_pause;

        // Обычный вид
        RemoteViews notificationView = createNotificationRemoteViews(
                false,
                playPauseIconRes,
                playPauseLargeIconRes,
                mShow.getNumber(),
                mShow.getName(),
                trackData,
                pendingPausePlayIntent,
                pendingForwardIntent,
                null,
                pendingCancelIntent);

        // Расширенный вид
        RemoteViews extendedNotificationView = createNotificationRemoteViews(
                true,
                playPauseIconRes,
                playPauseLargeIconRes,
                mShow.getNumber(),
                mShow.getName(),
                trackData,
                pendingPausePlayIntent,
                pendingForwardIntent,
                pendingRewindIntent,
                pendingCancelIntent);

        Notification notification = new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
                .setSmallIcon(smallIconDrawableRes)
                .setContentIntent(contentPI)
                .setAutoCancel(false)
                .setOngoing(true)
                .build();

        notification.contentView = notificationView;
        notification.bigContentView = extendedNotificationView;

        return notification;
    }

    private RemoteViews createNotificationRemoteViews(boolean isExtended,
                                                      int playPauseIconRes,
                                                      int playPauseLargeIconRes,
                                                      int showNumber,
                                                      String showName,
                                                      CueTrackData trackData,
                                                      PendingIntent pendingPausePlayIntent,
                                                      PendingIntent pendingForwardIntent,
                                                      PendingIntent pendingRewindIntent,
                                                      PendingIntent pendingCancelIntent) {
        int layout = isExtended ? R.layout.view_notification_extended : R.layout.view_notification;
        RemoteViews notificationView = new RemoteViews(getPackageName(), layout);

        if (trackData != null) {
            notificationView.setTextViewText(R.id.notif_trackname, trackData.getPerformer() + " – " + trackData.getTitle());
        } else {
            notificationView.setTextViewText(R.id.notif_trackname, getString(R.string.player_no_track_data_text));
        }

        notificationView.setTextViewText(R.id.notif_title, showNumber + " – " + showName);
        notificationView.setImageViewResource(R.id.notif_play_pause, playPauseIconRes);
        notificationView.setImageViewResource(R.id.notif_play_pause_large, playPauseLargeIconRes);

        notificationView.setOnClickPendingIntent(R.id.notif_play_pause, pendingPausePlayIntent);
        notificationView.setOnClickPendingIntent(R.id.notif_play_pause_large, pendingPausePlayIntent);
        notificationView.setOnClickPendingIntent(R.id.notif_forward, pendingForwardIntent);
        if (isExtended) {
            notificationView.setOnClickPendingIntent(R.id.notif_rewind, pendingRewindIntent);
        }
        notificationView.setOnClickPendingIntent(R.id.notif_close, pendingCancelIntent);
        return notificationView;
    }
    // endregion

    private static Intent getPlayPauseIntent(Context context) {
        Intent intent = new Intent(context, PodcastPlayerService.class);
        intent.setAction(MANIPULATION_ACTION_PLAY_PAUSE);
        return intent;
    }

    private static Intent getForwardIntent(Context context) {
        Intent intent = new Intent(context, PodcastPlayerService.class);
        intent.setAction(MANIPULATION_ACTION_DISCRETE_FORWARD);
        return intent;
    }

    private static Intent getRewindIntent(Context context) {
        Intent intent = new Intent(context, PodcastPlayerService.class);
        intent.setAction(MANIPULATION_ACTION_DISCRETE_REWIND);
        return intent;
    }

    private static Intent getCancelIntent(Context context) {
        Intent intent = new Intent(context, PodcastPlayerService.class);
        intent.setAction(MANIPULATION_ACTION_CANCEL);
        return intent;
    }

    //region Cue File
    private void startCueFileProcess(final ShowRelease showRelease) {
        mCueTrackDataArray = null;
        mCurrentTrackData = null;
        mCueFileHandler.removeCallbacksAndMessages(null);
        mExecutor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    final List<CueTrackData> result = CueHelper.getCueData(showRelease);
                    mCueFileHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            if (mShow != null && mShow.getNumber() == showRelease.getNumber()) {
                                mCueTrackDataArray = result;
                            }
                        }
                    });
                } catch (Exception e) {
                    Logger.e(TAG, "cueDataRunnable FAIL! " + e);
                    // Parse Exception? Delete invalid cueFile
                    CueHelper.deleteCueFile(showRelease);
                }
            }
        });
    }

    private CueTrackData getCueData(int time) {
        if (mCurrentTrackData != null && time >= mCurrentTrackData.getStartTime() && time < mCurrentTrackData.getStopTime()) {
            return mCurrentTrackData;
        } else if (mCueTrackDataArray != null) {
            for (CueTrackData trackData : mCueTrackDataArray) {
                if (time >= trackData.getStartTime() && time < trackData.getStopTime()) {
                    return onNewTrackData(trackData);
                }
            }
        }
        return null;
    }

    private CueTrackData onNewTrackData(CueTrackData trackData) {
        mCurrentTrackData = trackData;
        updateNotification(trackData);
        return mCurrentTrackData;
    }
    //endregion

    private void takePreviousShow(@NonNull ShowRelease lastPlayedShow) {
        if (mQueryHandler == null) {
            mQueryHandler = new ShowReleaseAsyncQueryHandler(getContentResolver(), new WeakReference<>(this));
        }

        String sortOrder = Contract.ShowReleaseTable.SHOW_NUMBER
                + (AerostatPrefs.getSortPlaylist(this) ? " ASC" : " DESC");

        String selection = null;

        switch (AerostatPrefs.getCurrentPlaylist(this)) {
            case FAVORITES:
                if (lastPlayedShow.isFavorite()) {
                    selection = Contract.ShowReleaseTable.SHOW_FAVORITE + " = 1";
                }
                break;
            case SAVED:
                if (lastPlayedShow.getSavedStatus() == ShowRelease.STATUS_SAVED) {
                    selection = Contract.ShowReleaseTable.SHOW_SAVED_STATUS + " != " + ShowRelease.STATUS_NOT_SAVED;
                }
                break;
        }

        mQueryHandler.startQuery(
                QUERY_PREVIOUS_SHOW_TOKEN,
                lastPlayedShow, // Passing to callback
                Contract.ShowReleaseTable.CONTENT_URI,
                null,
                selection,
                null,
                sortOrder
        );
    }

    private void takeNextShow(@NonNull ShowRelease lastPlayedShow) {
        if (mQueryHandler == null) {
            mQueryHandler = new ShowReleaseAsyncQueryHandler(getContentResolver(), new WeakReference<>(this));
        }

        String sortOrder = Contract.ShowReleaseTable.SHOW_NUMBER
                + (AerostatPrefs.getSortPlaylist(this) ? " ASC" : " DESC");

        String selection = null;

        switch (AerostatPrefs.getCurrentPlaylist(this)) {
            case FAVORITES:
                if (lastPlayedShow.isFavorite()) {
                    selection = Contract.ShowReleaseTable.SHOW_FAVORITE + " = 1";
                }
                break;
            case SAVED:
                if (lastPlayedShow.getSavedStatus() == ShowRelease.STATUS_SAVED) {
                    selection = Contract.ShowReleaseTable.SHOW_SAVED_STATUS + " != " + ShowRelease.STATUS_NOT_SAVED;
                }
                break;
        }

        mQueryHandler.startQuery(
                QUERY_NEXT_SHOW_TOKEN,
                lastPlayedShow, // Passing to callback
                Contract.ShowReleaseTable.CONTENT_URI,
                null,
                selection,
                null,
                sortOrder
        );
    }

    private void onNewShowTaken(@Nullable ShowRelease nextShowRelease) {
        Logger.d(TAG, "onNewShowTaken");
        if (nextShowRelease == null) {
            // Ничего не играет и нет следующего выпуска
            mListener.onPlayerRelease();
            hideNotification();
            abandonAudioFocus();
            player.release();
            if (!isBinded) {
                Logger.v(TAG, "PlayerService [stopSelf]");
                stopSelf();
            }
        } else {
            // Следующий выпуск взят
            openShowRelease(nextShowRelease, true, false);
        }
    }

    //region Restore last played show
    private void restoreLastPlayedShow(@NonNull AerostatPrefs.LastPlayed lastPlayed) {
        if (mQueryHandler == null) {
            mQueryHandler = new ShowReleaseAsyncQueryHandler(getContentResolver(), new WeakReference<>(this));
        }

        mQueryHandler.startQuery(
                QUERY_LAST_PLAYED_TOKEN,
                lastPlayed, // Passing to callback
                Contract.ShowReleaseTable.buildShowUri(lastPlayed.showNumber),
                null,
                null,
                null,
                null);
    }

    private void onLastPlayedShowRestored(@Nullable ShowRelease showRelease, int playedTime) {
        Logger.d(TAG, "onLastPlayedShowRestored playedTime: " + playedTime);
        if (showRelease == null) {
            // Ничего не играет и нечего восстанавливать
            mListener.onPlayerRelease();
        } else {
            // Последний выпуск восстановлен
            openShowRelease(showRelease, false, false);
        }
    }

    private void saveLastPlayed(@Nullable ShowRelease showRelease) {
        AerostatPrefs.LastPlayed lastPlayed = null;
        if (showRelease != null) {
            lastPlayed = new AerostatPrefs.LastPlayed(
                    showRelease.getNumber(), (int) player.getCurrentPosition());
        }
        AerostatPrefs.putLastPlayed(this, lastPlayed);
    }

    /**
     * Helps to work with provider asynchronously.
     * Static class to prevent Handler leaks.
     */
    private static class ShowReleaseAsyncQueryHandler extends AsyncQueryHandler {
        private WeakReference<PodcastPlayerService> mServiceWeakReference;

        public ShowReleaseAsyncQueryHandler(ContentResolver cr, WeakReference<PodcastPlayerService> serviceWeakReference) {
            super(cr);
            mServiceWeakReference = serviceWeakReference;
        }

        @Override
        protected void onQueryComplete(int token, Object cookie, Cursor cursor) {
            ShowRelease showRelease = null;


            if (mServiceWeakReference != null) {
                PodcastPlayerService service = mServiceWeakReference.get();
                if (service != null) {
                    switch (token) {
                        case QUERY_LAST_PLAYED_TOKEN:
                            AerostatPrefs.LastPlayed lastPlayed = (AerostatPrefs.LastPlayed) cookie;
                            if (cursor.moveToFirst() && lastPlayed != null) {
                                showRelease = Contract.ShowReleaseTable.getShowInfo(cursor);
                                service.onLastPlayedShowRestored(showRelease, lastPlayed.playedTime);
                            }
                            break;
                        case QUERY_NEXT_SHOW_TOKEN:
                            if (cursor.moveToFirst()) {
                                ShowRelease lastShow = (ShowRelease) cookie;

                                boolean finded;

                                do {
                                    showRelease = Contract.ShowReleaseTable.getShowInfo(cursor);
                                    finded = showRelease.getNumber() == lastShow.getNumber();
                                    if (finded) {
                                        break;
                                    }
                                } while (cursor.moveToNext());

                                if (finded && cursor.moveToNext()) {
                                    showRelease = Contract.ShowReleaseTable.getShowInfo(cursor);
                                    service.onNewShowTaken(showRelease);
                                }
                            }
                            break;
                        case QUERY_PREVIOUS_SHOW_TOKEN:
                            if (cursor.moveToFirst()) {
                                ShowRelease lastShow = (ShowRelease) cookie;

                                boolean finded;

                                do {
                                    showRelease = Contract.ShowReleaseTable.getShowInfo(cursor);
                                    finded = showRelease.getNumber() == lastShow.getNumber();
                                    if (finded) {
                                        break;
                                    }
                                } while (cursor.moveToNext());

                                if (finded && cursor.moveToPrevious()) {
                                    showRelease = Contract.ShowReleaseTable.getShowInfo(cursor);
                                    service.onNewShowTaken(showRelease);
                                }
                            }
                            break;
                    }
                    cursor.close();
                }
            }
        }
    }
    //endregion

    // region PlayerCallback listener
    @Override
    public void onCompletion() {
        handleStop();
    }

    @Override
    public void onPlaying() {
        handleStart();
    }

    @Override
    public void onPaused() {
        handlePause();
    }

    @Override
    public void onPrepared() {

        if (mStartPlayTime > 0) {
            player.seekTo(mStartPlayTime);
            updateProgressWithTrackData();
        }

        if (mShouldStartWhenReady) {
            player.play();
        } else {
            player.pause();
        }
    }

    @Override
    public void onPreparing() {
        if (mListener != null) {
            mListener.onPlayerWait(mShow);
        }
    }

    @Override
    public void onError(String error) {
        handleError(error);
    }
    // endregion

    public class LocalBinder extends Binder {
        public PlayerManipulationInterface getService() {
            return PodcastPlayerService.this;
        }
    }

    public interface PodcastPlayerCallback {
        void onPlayerWait(ShowRelease showRelease);

        void onPlayerStart(ShowRelease showRelease);

        void onPlayerPause(ShowRelease showRelease);

        void onPlayerStop(ShowRelease showRelease);

        void onPlayerError(ShowRelease showRelease);

        void onPlayerRelease();

        void onPlayerProgress(ShowRelease showRelease, int currentTime, int fullTime, CueTrackData trackData);

        void onPlayerFileNotExist(ShowRelease showRelease);
    }

    private class HeadsetBroadCastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Logger.i(TAG, "HeadsetBroadCastReceiver. action: " + action);
            if ((action.compareTo(Intent.ACTION_HEADSET_PLUG)) == 0) {
                int headSetState = intent.getIntExtra("state", 0);
                if (headSetState == 0 && player.isPlaying()) {
                    player.pause();
                }
            } else if (BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED.equals(action)) {
                int extraData = intent.getIntExtra(BluetoothHeadset.EXTRA_STATE, BluetoothHeadset.STATE_DISCONNECTED);
                if (extraData == BluetoothHeadset.STATE_DISCONNECTED) {
                    Logger.i(TAG, "STATE_DISCONNECTED");
                    player.pause();
                }
            }
        }
    }

    private void safeStartForeground(int id, Notification notification) {
        try {
            startForeground(id, notification);
        } catch (Exception e) {
            Logger.logToCrashlytics("Failed to startForeground", e);
        }
    }
}