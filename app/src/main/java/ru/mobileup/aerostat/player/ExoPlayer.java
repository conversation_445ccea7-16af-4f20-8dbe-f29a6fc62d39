package ru.mobileup.aerostat.player;

import android.content.Context;
import android.net.Uri;

import com.google.android.exoplayer2.DefaultLoadControl;
import com.google.android.exoplayer2.ExoPlaybackException;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.extractor.DefaultExtractorsFactory;
import com.google.android.exoplayer2.extractor.ExtractorsFactory;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.source.TrackGroupArray;
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector;
import com.google.android.exoplayer2.trackselection.TrackSelectionArray;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultBandwidthMeter;
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
import com.google.android.exoplayer2.util.Util;

import ru.mobileup.aerostat.util.Logger;

/**
 * Created by roman on 15.02.17.
 */

public class ExoPlayer implements Player {
    private static final String TAG = "ExoPlayer";

    private SimpleExoPlayer simpleExoPlayer;
    private PlayerCallback listener;
    private Context context;

    private PlayerState currentState;

    public ExoPlayer(Context context) {
        this.context = context;
        currentState = new EMPTY();
    }

    @Override
    public PlayerState getCurrentState() {
        return currentState;
    }

    @Override
    public void prepare(Uri uri) {
        currentState.prepare(uri);
    }

    @Override
    public void play() {
        currentState.play();
    }

    @Override
    public void pause() {
        currentState.pause();
    }

    @Override
    public boolean isPlaying() {
        return currentState.isPlaying();
    }

    @Override
    public void seekTo(long timeMs) {
        currentState.seekTo(timeMs);
    }

    @Override
    public void setVolume(float audioVolume) {
        currentState.setVolume(audioVolume);
    }

    @Override
    public long getCurrentPosition() {
        return currentState.getCurrentPosition();
    }

    @Override
    public long getDuration() {
        return currentState.getDuration();
    }

    @Override
    public void release() {
        currentState.release();
    }

    @Override
    public boolean isReleased() {
        return currentState.isReleased();
    }

    @Override
    public void setListener(PlayerCallback listener) {
        this.listener = listener;
    }

    // region state machine
    private class State implements PlayerState {

        public void prepare(Uri uri) {
            Logger.e(TAG, "illegal state");
        }

        public void prepared() {
            Logger.e(TAG, "illegal state");
        }

        public void play() {
            Logger.e(TAG, "illegal state");
        }

        public void completed() {
            Logger.e(TAG, "illegal state");
        }

        public void pause() {
            Logger.e(TAG, "illegal state");
        }

        public boolean isPaused() { return false; }

        public boolean isPlaying() {
            return false;
        }

        public void seekTo(long timeMs) {
            Logger.e(TAG, "illegal state");
        }

        public long getCurrentPosition() {
            return 0;
        }

        public long getDuration() {
            return 0;
        }

        public void setVolume(float audioVolume) {
            Logger.e(TAG, "illegal state");
        }

        public void release() {
            Logger.d(TAG, "release");
            currentState = new EMPTY();
            if (simpleExoPlayer != null) {
                simpleExoPlayer.stop();
                simpleExoPlayer.release();
                simpleExoPlayer = null;
            }
        }

        public boolean isReleased() {
            Logger.e(TAG, "illegal state");
            return true;
        }

        public void error(String error) {
            Logger.d(TAG, "error");
            currentState = new ERROR();
            if (listener != null) {
                listener.onError(error);
            }
        }
    }

    private class EMPTY extends State {
        @Override
        public void prepare(Uri uri) {
            Logger.d(TAG, "prepare uri: " + uri);

            PREPARING preparing = new PREPARING();
            currentState = preparing;
            if (listener != null) {
                listener.onPreparing();
            }
            preparing.begin(uri);
        }
    }

    private class PREPARING extends State {
        private void begin(Uri uri) {
            simpleExoPlayer = new SimpleExoPlayer.Builder(context)
                    .setTrackSelector( new DefaultTrackSelector())
                    .setLoadControl(new DefaultLoadControl())
                    .build();

            simpleExoPlayer.addListener(new ExoPlayerEventListener());

            DataSource.Factory dataFactory = new DefaultDataSourceFactory(context,
                    Util.getUserAgent(context, "Aerostat"),
                    new DefaultBandwidthMeter());
            ExtractorsFactory extractorsFactory = new DefaultExtractorsFactory();
            MediaSource audioSource = new ProgressiveMediaSource.Factory(dataFactory, extractorsFactory).createMediaSource(
                    MediaItem.fromUri(uri)
            );

            simpleExoPlayer.prepare(audioSource);
        }

        @Override
        public void prepared() {
            Logger.d(TAG, "prepared");
            currentState = new PREPARED();
            if (listener != null) {
                listener.onPrepared();
            }
        }

        @Override
        public boolean isReleased() {
            return false;
        }
    }

    private class PREPARED extends State {
        @Override
        public void play() {
            Logger.d(TAG, "play");
            simpleExoPlayer.setPlayWhenReady(true);
            currentState = new PLAYING();
            if (listener != null) {
                listener.onPlaying();
            }
        }

        @Override
        public void pause() {
            Logger.d(TAG, "pause");
            simpleExoPlayer.setPlayWhenReady(false);
            currentState = new PAUSED();
            if (listener != null) {
                listener.onPaused();
            }
        }

        @Override
        public void seekTo(long timeMs) {
            Logger.d(TAG, "seekTo: " + timeMs);
            simpleExoPlayer.seekTo(timeMs);
        }

        @Override
        public void setVolume(float audioVolume) {
            Logger.d(TAG, "setVolume");
            simpleExoPlayer.setVolume(audioVolume);
        }

        @Override
        public long getCurrentPosition() {
            return simpleExoPlayer.getCurrentPosition();
        }

        @Override
        public long getDuration() {
            return simpleExoPlayer.getDuration();
        }

        @Override
        public boolean isReleased() {
            return false;
        }
    }

    private class PLAYING extends State {
        @Override
        public void pause() {
            Logger.d(TAG, "pause");
            simpleExoPlayer.setPlayWhenReady(false);
            currentState = new PAUSED();
            if (listener != null) {
                listener.onPaused();
            }
        }

        @Override
        public void seekTo(long timeMs) {
            Logger.d(TAG, "seekTo: " + timeMs);
            simpleExoPlayer.seekTo(timeMs);
        }

        @Override
        public void setVolume(float audioVolume) {
            Logger.d(TAG, "setVolume");
            simpleExoPlayer.setVolume(audioVolume);
        }

        @Override
        public void completed() {
            Logger.d(TAG, "completed");
            currentState = new COMPLETED();
            if (listener != null) {
                listener.onCompletion();
            }
        }

        @Override
        public boolean isPlaying() {
            return true;
        }

        @Override
        public long getCurrentPosition() {
            return simpleExoPlayer.getCurrentPosition();
        }

        @Override
        public long getDuration() {
            return simpleExoPlayer.getDuration();
        }

        @Override
        public boolean isReleased() {
            return false;
        }
    }

    private class PAUSED extends State {
        @Override
        public void play() {
            Logger.d(TAG, "play");
            simpleExoPlayer.setPlayWhenReady(true);
            currentState = new PLAYING();
            if (listener != null) {
                listener.onPlaying();
            }
        }

        @Override
        public void seekTo(long timeMs) {
            Logger.d(TAG, "seekTo: " + timeMs);
            simpleExoPlayer.seekTo(timeMs);
        }

        @Override
        public void setVolume(float audioVolume) {
            Logger.d(TAG, "setVolume");
            simpleExoPlayer.setVolume(audioVolume);
        }

        @Override
        public long getCurrentPosition() {
            return simpleExoPlayer.getCurrentPosition();
        }

        @Override
        public long getDuration() {
            return simpleExoPlayer.getDuration();
        }

        @Override
        public boolean isReleased() {
            return false;
        }

        @Override
        public boolean isPaused() {
            return true;
        }
    }

    private class COMPLETED extends State {
        @Override
        public boolean isReleased() {
            return false;
        }
    }

    private class ERROR extends State {

        @Override
        public long getCurrentPosition() {
            return simpleExoPlayer.getCurrentPosition();
        }

        @Override
        public long getDuration() {
            return simpleExoPlayer.getDuration();
        }

        @Override
        public boolean isReleased() {
            return false;
        }
    }
    // endregion

    private class ExoPlayerEventListener implements com.google.android.exoplayer2.ExoPlayer.EventListener {

        private boolean isPrepared = false;

        @Override
        public void onTracksChanged(TrackGroupArray trackGroups, TrackSelectionArray trackSelections) {
            // do nothing
        }

        @Override
        public void onLoadingChanged(boolean isLoading) {
            // do nothing
        }

        @Override
        public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
            Logger.d(TAG, "onPlayerStateChanged playbackState: " + playbackState + "; isPrepared: " + isPrepared);
            switch (playbackState) {
                case com.google.android.exoplayer2.ExoPlayer.STATE_READY:
                    if (!isPrepared) {
                        isPrepared = true;
                        currentState.prepared();
                    }
                    break;
                case com.google.android.exoplayer2.ExoPlayer.STATE_ENDED:
                    currentState.completed();
                    break;
            }
        }

        @Override
        public void onPlayerError(ExoPlaybackException error) {
            currentState.error(error.getMessage());
        }
    }
}
