package ru.mobileup.aerostat.player

import android.net.Uri

interface PlayerState {

    fun prepare(uri: Uri?)

    fun prepared()

    fun play()

    fun isPlaying(): Boolean

    fun completed()

    fun pause()

    fun isPaused(): Boolean

    fun seekTo(timeMs: Long)

    fun getCurrentPosition(): Long

    fun getDuration(): Long

    fun setVolume(audioVolume: Float)

    fun release()

    fun isReleased(): Boolean

    fun error(error: String?)
}