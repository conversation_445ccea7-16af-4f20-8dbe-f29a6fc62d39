package ru.mobileup.aerostat.player;

import android.content.Context;
import android.media.AudioManager;
import android.net.Uri;
import android.os.PowerManager;

import java.io.IOException;

import ru.mobileup.aerostat.util.Logger;

/**
 * Created by roman on 15.02.17.
 */

public class MediaPlayer implements Player {

    private static final String TAG = "MediaPlayer";

    private android.media.MediaPlayer mediaPlayer;
    private PlayerCallback listener;
    private Context context;

    private PlayerState currentState;

    private android.media.MediaPlayer.OnPreparedListener onPreparedListener =
            new android.media.MediaPlayer.OnPreparedListener() {
                @Override
                public void onPrepared(android.media.MediaPlayer mp) {
                    currentState.prepared();
                }
            };
    private android.media.MediaPlayer.OnCompletionListener onCompletionListener =
            new android.media.MediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(android.media.MediaPlayer mp) {
                    currentState.completed();
                }
            };
    private android.media.MediaPlayer.OnErrorListener onErrorListener =
            new android.media.MediaPlayer.OnErrorListener() {
                @Override
                public boolean onError(android.media.MediaPlayer mp, int what, int extra) {
                    currentState.error(String.format("what: %s; extra: %s", what, extra));
                    return true;
                }
            };

    public MediaPlayer(Context context) {
        this.context = context;
        currentState = new EMPTY();
    }

    @Override
    public PlayerState getCurrentState() {
        return currentState;
    }

    @Override
    public void prepare(Uri uri) {
       currentState.prepare(uri);
    }

    @Override
    public void play() {
        currentState.play();
    }

    @Override
    public void pause() {
        currentState.pause();
    }

    @Override
    public boolean isPlaying() {
        return currentState.isPlaying();
    }

    @Override
    public void seekTo(long timeMs) {
        currentState.seekTo(timeMs);
    }

    @Override
    public void setVolume(float audioVolume) {
        currentState.setVolume(audioVolume);
    }

    @Override
    public long getCurrentPosition() {
        return currentState.getCurrentPosition();
    }

    @Override
    public long getDuration() {
        return currentState.getDuration();
    }

    @Override
    public void release() {
        currentState.release();
    }

    @Override
    public boolean isReleased() {
        return currentState.isReleased();
    }

    @Override
    public void setListener(PlayerCallback listener) {
        this.listener = listener;
    }

    // region state machine
    private class State implements PlayerState {

        public void prepare(Uri uri) {
            Logger.e(TAG, "illegal state");
        }

        public void prepared() {
            Logger.e(TAG, "illegal state");
        }

        public void play() {
            Logger.e(TAG, "illegal state");
        }

        public void completed() {
            Logger.e(TAG, "illegal state");
        }

        public void pause() {
            Logger.e(TAG, "illegal state");
        }

        public boolean isPaused() {
            return false;
        }

        public boolean isPlaying() {
            return false;
        }

        public void seekTo(long timeMs) {
            Logger.e(TAG, "illegal state");
        }

        public long getCurrentPosition() {
            return 0;
        }

        public long getDuration() {
            return 0;
        }

        public void setVolume(float audioVolume) {
            Logger.e(TAG, "illegal state");
        }

        public void release() {
            Logger.d(TAG, "release");
            currentState = new EMPTY();
            if (mediaPlayer != null) {
                mediaPlayer.release();
                mediaPlayer = null;
            }
        }

        public boolean isReleased() {
            Logger.e(TAG, "illegal state");
            return true;
        }

        public void error(String error) {
            Logger.d(TAG, "error");
            currentState = new ERROR();
            if (listener != null) {
                listener.onError(error);
            }
        }
    }

    private class EMPTY extends State {
        @Override
        public void prepare(Uri uri) {
            Logger.d(TAG, "prepare uri: " + uri);

            PREPARING preparing = new PREPARING();
            currentState = preparing;
            if (listener != null) {
                listener.onPreparing();
            }
            preparing.begin(uri);
        }
    }

    private class PREPARING extends State {
        private void begin(Uri uri) {
            mediaPlayer = new android.media.MediaPlayer();
            try {
                mediaPlayer.setWakeMode(context, PowerManager.PARTIAL_WAKE_LOCK);
                mediaPlayer.setDataSource(context, uri);
                mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);

                mediaPlayer.setOnPreparedListener(onPreparedListener);
                mediaPlayer.setOnCompletionListener(onCompletionListener);
                mediaPlayer.setOnErrorListener(onErrorListener);

                mediaPlayer.prepareAsync();
            } catch (IOException e) {
                Logger.d(TAG, "mediaPlayer error: " + e);
                error(e.getMessage());
            }
        }

        @Override
        public void prepared() {
            Logger.d(TAG, "prepared");
            currentState = new PREPARED();
            if (listener != null) {
                listener.onPrepared();
            }
        }

        @Override
        public boolean isReleased() {
            return false;
        }
    }

    private class PREPARED extends State {
        @Override
        public void play() {
            Logger.d(TAG, "play");
            mediaPlayer.start();
            currentState = new PLAYING();
            if (listener != null) {
                listener.onPlaying();
            }
        }

        @Override
        public void pause() {
            Logger.d(TAG, "pause");
            currentState = new PAUSED();
            if (listener != null) {
                listener.onPaused();
            }
        }

        @Override
        public void seekTo(long timeMs) {
            Logger.d(TAG, "seekTo: " + timeMs);
            mediaPlayer.seekTo((int) timeMs);
        }

        @Override
        public void setVolume(float audioVolume) {
            Logger.d(TAG, "setVolume");
            mediaPlayer.setVolume(audioVolume, audioVolume);
        }

        @Override
        public long getCurrentPosition() {
            return mediaPlayer.getCurrentPosition();
        }

        @Override
        public long getDuration() {
            return mediaPlayer.getDuration();
        }

        @Override
        public boolean isReleased() {
            return false;
        }
    }

    private class PLAYING extends State {
        @Override
        public void pause() {
            Logger.d(TAG, "pause");
            mediaPlayer.pause();
            currentState = new PAUSED();
            if (listener != null) {
                listener.onPaused();
            }
        }

        @Override
        public void seekTo(long timeMs) {
            Logger.d(TAG, "seekTo: " + timeMs);
            mediaPlayer.seekTo((int) timeMs);
        }

        @Override
        public void setVolume(float audioVolume) {
            Logger.d(TAG, "setVolume");
            mediaPlayer.setVolume(audioVolume, audioVolume);
        }

        @Override
        public void completed() {
            Logger.d(TAG, "completed");
            currentState = new COMPLETED();
            if (listener != null) {
                listener.onCompletion();
            }
        }

        @Override
        public boolean isPlaying() {
            return true;
        }

        @Override
        public long getCurrentPosition() {
            return mediaPlayer.getCurrentPosition();
        }

        @Override
        public long getDuration() {
            return mediaPlayer.getDuration();
        }

        @Override
        public boolean isReleased() {
            return false;
        }
    }

    private class PAUSED extends State {
        @Override
        public void play() {
            Logger.d(TAG, "play");
            mediaPlayer.start();
            currentState = new PLAYING();
            if (listener != null) {
                listener.onPlaying();
            }
        }

        @Override
        public void seekTo(long timeMs) {
            Logger.d(TAG, "seekTo: " + timeMs);
            mediaPlayer.seekTo((int) timeMs);
        }

        @Override
        public void setVolume(float audioVolume) {
            Logger.d(TAG, "setVolume");
            mediaPlayer.setVolume(audioVolume, audioVolume);
        }

        @Override
        public long getCurrentPosition() {
            return mediaPlayer.getCurrentPosition();
        }

        @Override
        public long getDuration() {
            return mediaPlayer.getDuration();
        }

        @Override
        public boolean isReleased() {
            return false;
        }

        @Override
        public boolean isPaused() {
            return true;
        }
    }

    private class COMPLETED extends State {
        @Override
        public boolean isReleased() {
            return false;
        }
    }

    private class ERROR extends State {

        @Override
        public long getCurrentPosition() {
            return mediaPlayer.getCurrentPosition();
        }

        @Override
        public long getDuration() {
            return mediaPlayer.getDuration();
        }

        @Override
        public boolean isReleased() {
            return false;
        }
    }
    // endregion
}
