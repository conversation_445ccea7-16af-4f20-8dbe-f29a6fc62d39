package ru.mobileup.aerostat.storage

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Environment
import android.os.Handler
import android.os.Looper
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import com.tonyodev.fetch2.AbstractFetchListener
import com.tonyodev.fetch2.Download
import com.tonyodev.fetch2.EnqueueAction
import com.tonyodev.fetch2.Error
import com.tonyodev.fetch2.Fetch
import com.tonyodev.fetch2.FetchConfiguration
import com.tonyodev.fetch2.HttpUrlConnectionDownloader
import com.tonyodev.fetch2.Request
import com.tonyodev.fetch2core.Downloader.FileDownloaderType
import com.tonyodev.fetch2core.Func
import kotlinx.coroutines.CancellationException
import ru.mobileup.aerostat.AerostatApplication
import ru.mobileup.aerostat.R
import ru.mobileup.aerostat.api.model.ShowRelease
import ru.mobileup.aerostat.cue.CueHelper
import ru.mobileup.aerostat.util.FileUtils
import ru.mobileup.aerostat.util.Logger
import ru.mobileup.aerostat.util.ShowReleaseUtils

// ATTENTION: для скачивания файлов используется устаревшая библиотека https://github.com/tonyofrancis/Fetch
// В ней есть баги - https://github.com/tonyofrancis/Fetch/issues/created_by/aartikov, которые удалось обойти костылями на нашей стороне.
// До этого использовалась библиотека https://github.com/lingochamp/okdownload но с ней тоже были проблемы (подробности не удалось вспомнить).
// Других хороших альтернатив не нашли.

@SuppressLint("StaticFieldLeak")
object DownloadManager {

    private const val DOWNLOAD_CONCURRENT_LIMIT = 5
    private const val AUTO_RETRY_MAX_ATTEMPTS = 3
    private const val AUDIO_CACHE_DIR_NAME = "AerostatAudio"

    private val DEFAULT_DOWNLOAD_PATH =
        Uri.parse(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC).path)
            .buildUpon()
            .appendPath(AUDIO_CACHE_DIR_NAME)
            .build()
            .path

    private val context get() = AerostatApplication.getInstance()

    private val fetch: Fetch by lazy {
        val configuration = FetchConfiguration.Builder(context)
            .setDownloadConcurrentLimit(DOWNLOAD_CONCURRENT_LIMIT)
            .setAutoRetryMaxAttempts(/*AUTO_RETRY_MAX_ATTEMPTS*/ 0) // Workaround for https://github.com/tonyofrancis/Fetch/issues/551
            .enableRetryOnNetworkGain(/* true */ false) // Workaround for https://github.com/tonyofrancis/Fetch/issues/551
            .enableLogging(true)
            .setLogger(CrashlyticsFetchLogger())
            .setHttpDownloader(HttpUrlConnectionDownloader(FileDownloaderType.PARALLEL))
            .setNotificationManager(object : DownloadNotificationManager(context) {
                override fun getFetchInstanceForNamespace(namespace: String): Fetch {
                    return fetch
                }
            })
            .build()

        Fetch.getInstance(configuration).apply {
            addListener(DownloadListener())
        }
    }

    private var activity: Activity? = null

    fun setActivity(activity: Activity) {
        this.activity = activity
    }

    fun resetActivity() {
        activity = null
    }

    fun getDownloadDirectory(): Uri {
        return Uri.parse(AerostatPrefs.getDownloadPath(context, DEFAULT_DOWNLOAD_PATH))
    }

    fun setDownloadDirectory(uri: Uri) {
        AerostatPrefs.putDownloadPath(context, uri.toString())
    }

    fun isDownloadDirectoryAvailable(): Boolean {
        val directoryUri = getDownloadDirectory()
        val isDefaultDirectory = directoryUri == Uri.parse(DEFAULT_DOWNLOAD_PATH)
        val hasStoragePermission = ContextCompat.checkSelfPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED // пермишен мог быть выдан на старой версии приложения
        val canReadAndWrite = FileUtils.canReadAndWrite(directoryUri, context)
        return (isDefaultDirectory && hasStoragePermission) || canReadAndWrite
    }

    fun downloadShowOnDisk(showRelease: ShowRelease) {
        val showUri = try {
            getDownloadDirectory().toTreeDocumentFile()!!.createFile("audio/mpeg", showRelease.number.toString())!!.uri
        } catch (e: CancellationException) {
            throw e
        } catch (e: Exception) {
            Logger.logToCrashlytics("Failed to get showUri, download directory: ${getDownloadDirectory()}", e)
            e.printStackTrace()
            showError(R.string.downloading_disk_error)
            return
        }

        val request = Request(showRelease.fileUrl, showUri)
        request.enqueueAction = EnqueueAction.UPDATE_ACCORDINGLY
        request.identifier = showRelease.number.toLong()

        fetch.enqueue(
            request,
            null,
            Func { error: Error ->
                Logger.logToCrashlytics(Exception("Failed to enqueue request ${request.url} ${request.file}"))
                showError(mapFetchError(error))
            }
        )

    }

    fun cancelDownload(showRelease: ShowRelease) {
        fetch.cancel(showRelease.downloadingIndex.toInt())
    }

    fun removeShow(showRelease: ShowRelease) {
        removeFile(showRelease)

        showRelease.savedStatus = ShowRelease.STATUS_NOT_SAVED
        showRelease.savedFilePath = null
        showRelease.downloadingIndex = 0

        update(showRelease)
    }

    private fun removeFile(showRelease: ShowRelease) {
        showRelease.savedFilePath?.let {
            Uri.parse(it).toDocumentFile()?.takeIf { it.exists() }?.delete()
        }
    }

    private fun Uri.toDocumentFile() = FileUtils.uriToDocumentFile(this, context)

    private fun Uri.toTreeDocumentFile() = FileUtils.uriToTreeDocumentFile(this, context)

    fun checkAndFixDownloadStatus(showRelease: ShowRelease) {
        try {
            when (showRelease.savedStatus) {
                ShowRelease.STATUS_DOWNLOADING -> {
                    // Workaround for https://github.com/tonyofrancis/Fetch/issues/551
                    fetch.remove(showRelease.downloadingIndex.toInt())
                    removeShow(showRelease)
                    downloadShowOnDisk(showRelease)
                    //

                    //fetch.resume(showRelease.downloadingIndex.toInt())
                }
                ShowRelease.STATUS_SAVED -> {
                    showRelease.savedFilePath?.let { savedFilePath ->
                        val documentFile = Uri.parse(savedFilePath).toDocumentFile()

                        if (documentFile == null || documentFile.exists().not()) {
                            showRelease.savedStatus = ShowRelease.STATUS_NOT_SAVED
                            showRelease.savedFilePath = null
                            update(showRelease)
                        }
                    }
                }
                ShowRelease.STATUS_NOT_SAVED -> {
                    val documentFile = getDownloadDirectory().toTreeDocumentFile()?.findFile("${showRelease.number}.mp3")

                    if (documentFile != null) {
                        showRelease.savedStatus = ShowRelease.STATUS_SAVED
                        showRelease.savedFilePath = documentFile.uri.toString()
                        update(showRelease)
                    }
                }
            }
        } catch (e: CancellationException) {
            throw e
        } catch (e: Exception) {
            Logger.logToCrashlytics("Failed to checkAndFixDownloadStatus", e)
            e.printStackTrace()
        }
    }

    private fun update(showRelease: ShowRelease) {
        ShowReleaseUtils.updateDownloadingState(showRelease, context)
    }

    private fun showError(@StringRes errorRes: Int) {
        Handler(Looper.getMainLooper()).post {
            activity?.runCatching {
                ShowReleaseUtils.showDownloadErrorDialog(this, errorRes)
            }
        }
    }

    private fun mapFetchError(error: Error): Int = when (error) {
        Error.FILE_NOT_CREATED,
        Error.WRITE_PERMISSION_DENIED,
        Error.NO_STORAGE_SPACE,
        Error.FILE_NOT_FOUND,
        Error.FAILED_TO_RENAME_INCOMPLETE_DOWNLOAD_FILE,
        Error.FAILED_TO_RENAME_FILE,
        Error.FILE_ALLOCATION_FAILED -> R.string.downloading_disk_error


        Error.NO_NETWORK_CONNECTION,
        Error.CONNECTION_TIMED_OUT,
        Error.UNKNOWN_IO_ERROR,
        Error.UNKNOWN_HOST -> R.string.downloading_network_error


        Error.HTTP_NOT_FOUND,
        Error.EMPTY_RESPONSE_FROM_SERVER,
        Error.REQUEST_NOT_SUCCESSFUL,
        Error.HTTP_CONNECTION_NOT_ALLOWED,
        Error.INVALID_CONTENT_HASH -> R.string.downloading_server_error

        Error.UNKNOWN,
        Error.NONE,
        Error.REQUEST_ALREADY_EXIST,
        Error.DOWNLOAD_NOT_FOUND,
        Error.FETCH_DATABASE_ERROR,
        Error.REQUEST_WITH_ID_ALREADY_EXIST,
        Error.REQUEST_WITH_FILE_PATH_ALREADY_EXIST,
        Error.FETCH_FILE_SERVER_URL_INVALID,
        Error.FAILED_TO_UPDATE_REQUEST,
        Error.FAILED_TO_ADD_COMPLETED_DOWNLOAD,
        Error.FETCH_FILE_SERVER_INVALID_RESPONSE,
        Error.REQUEST_DOES_NOT_EXIST,
        Error.ENQUEUE_NOT_SUCCESSFUL,
        Error.COMPLETED_NOT_ADDED_SUCCESSFULLY,
        Error.ENQUEUED_REQUESTS_ARE_NOT_DISTINCT -> R.string.downloading_other_error
    }

    class DownloadListener : AbstractFetchListener() {

        override fun onAdded(download: Download) {
            val showRelease = download.getShowRelease() ?: return
            showRelease.savedStatus = ShowRelease.STATUS_DOWNLOADING
            showRelease.savedFilePath = download.file
            showRelease.downloadingIndex = download.id.toLong()
            update(showRelease)

            Thread {
                try {
                    CueHelper.downloadCueDataIfNeeded(showRelease)
                } catch (e: CancellationException) {
                    throw e
                } catch (e: Exception) {
                    Logger.logToCrashlytics("Failed to download cue data for show ${showRelease.number}", e)
                    e.printStackTrace()
                    //не удалось скачать cue файл, повторно скачает при воспроизведении
                }
            }.start()

            Logger.logToCrashlytics(
                "Start downloading for show ${showRelease.number}, url ${download.url}, file ${download.file}"
            )
        }

        override fun onCompleted(download: Download) {
            fetch.remove(download.id) // Workaround for https://github.com/tonyofrancis/Fetch/issues/553
            val showRelease = download.getShowRelease() ?: return
            showRelease.savedStatus = ShowRelease.STATUS_SAVED
            showRelease.savedFilePath = download.file
            showRelease.downloadingIndex = 0
            update(showRelease)
        }

        override fun onError(download: Download, error: Error, throwable: Throwable?) {
            val errorMessage = "Downloading onError: ${download.url} ${download.file} $error"
            Logger.logToCrashlytics(errorMessage, throwable ?: Exception(errorMessage))
            throwable?.printStackTrace()
            showError(mapFetchError(error))
            fetch.remove(download.id)  // Workaround for https://github.com/tonyofrancis/Fetch/issues/551
            val showRelease = download.getShowRelease() ?: return
            removeShow(showRelease)
        }

        override fun onCancelled(download: Download) {
            fetch.remove(download.id)  // Workaround for https://github.com/tonyofrancis/Fetch/issues/551
            val showRelease = download.getShowRelease() ?: return
            removeShow(showRelease)
        }

        private fun Download.getShowRelease() = ShowReleaseUtils.getShowRelease(identifier.toInt(), context)
    }
}