package ru.mobileup.aerostat.storage.provider;

import android.content.ContentValues;

import ru.mobileup.aerostat.api.model.ShowRelease;

/**
 * Created by terrakok on 16.01.15.
 */
public class ContentHelper {

    private ContentHelper() {
    }

    public static ContentValues createShowReleaseContentValues(ShowRelease show) {
        ContentValues cv = new ContentValues();

        cv.put(Contract.ShowReleaseTable.SHOW_NUMBER, show.getNumber());
        cv.put(Contract.ShowReleaseTable.SHOW_NAME, show.getName());
        cv.put(Contract.ShowReleaseTable.SHOW_NAME_SEARCH, show.getNumber() + " " + show.getName().toUpperCase()); //поле для поиска по названию
        cv.put(Contract.ShowReleaseTable.SHOW_SUBTITLE, show.getSubTitle());
        cv.put(Contract.ShowReleaseTable.SHOW_DESCRIPTION, show.getDescription());
        cv.put(Contract.ShowReleaseTable.SHOW_DESCRIPTION_SEARCH, show.getDescription().toUpperCase()); //поле для поиска по содержанию
        cv.put(Contract.ShowReleaseTable.SHOW_FILE_URL, show.getFileUrl());
        cv.put(Contract.ShowReleaseTable.SHOW_CUE_URL, show.getCueUrl());
        cv.put(Contract.ShowReleaseTable.SHOW_FAVORITE, show.isFavorite());
        cv.put(Contract.ShowReleaseTable.SHOW_SAVED_STATUS, show.getSavedStatus());
        cv.put(Contract.ShowReleaseTable.SHOW_DOWNLOADING_INDEX, show.getDownloadingIndex());
        cv.put(Contract.ShowReleaseTable.SHOW_SAVED_FILE_PATH, show.getSavedFilePath());
        cv.put(Contract.ShowReleaseTable.SHOW_LISTENING_PROGRESS, show.getListeningProgress());

        return cv;
    }

    public static ContentValues createFavoriteContentValues(ShowRelease show) {
        ContentValues cv = new ContentValues();
        cv.put(Contract.ShowReleaseTable.SHOW_FAVORITE, show.isFavorite());
        return cv;
    }

    public static ContentValues createDownloadingStateValues(ShowRelease show) {
        ContentValues cv = new ContentValues();
        cv.put(Contract.ShowReleaseTable.SHOW_SAVED_STATUS, show.getSavedStatus());
        cv.put(Contract.ShowReleaseTable.SHOW_DOWNLOADING_INDEX, show.getDownloadingIndex());
        cv.put(Contract.ShowReleaseTable.SHOW_SAVED_FILE_PATH, show.getSavedFilePath());
        return cv;
    }


    public static ContentValues createListeningProgressContentValues(ShowRelease show) {
        ContentValues cv = new ContentValues();
        cv.put(Contract.ShowReleaseTable.SHOW_LISTENING_PROGRESS, show.getListeningProgress());
        return cv;
    }

    /**
     * Создает ContentValues для обновления существующих выпусков.
     * Обновляет только те поля, которые могут изменяться на сервере,
     * не затрагивая пользовательские данные (избранное, прогресс, загрузки).
     */
    public static ContentValues createShowReleaseUpdateContentValues(ShowRelease show) {
        ContentValues cv = new ContentValues();

        cv.put(Contract.ShowReleaseTable.SHOW_NUMBER, show.getNumber()); // нужен для поиска по номеру
        // Обновляем только серверные данные, но не трогаем локальные
        cv.put(Contract.ShowReleaseTable.SHOW_NAME, show.getName());
        cv.put(Contract.ShowReleaseTable.SHOW_NAME_SEARCH, show.getNumber() + " " + show.getName().toUpperCase());
        cv.put(Contract.ShowReleaseTable.SHOW_SUBTITLE, show.getSubTitle());
        cv.put(Contract.ShowReleaseTable.SHOW_DESCRIPTION, show.getDescription());
        cv.put(Contract.ShowReleaseTable.SHOW_DESCRIPTION_SEARCH, show.getDescription().toUpperCase());
        cv.put(Contract.ShowReleaseTable.SHOW_FILE_URL, show.getFileUrl());
        cv.put(Contract.ShowReleaseTable.SHOW_CUE_URL, show.getCueUrl());
        return cv;
    }
}
