package ru.mobileup.aerostat.storage

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import androidx.core.app.NotificationCompat
import com.tonyodev.fetch2.*
import com.tonyodev.fetch2.util.DEFAULT_NOTIFICATION_TIMEOUT_AFTER_RESET
import ru.mobileup.aerostat.R
import com.tonyodev.fetch2.R as Fetch2R

abstract class DownloadNotificationManager(private val context: Context) : DefaultFetchNotificationManager(context) {

    // Copied from DefaultFetchNotificationManager to hide Pause button
    override fun updateNotification(notificationBuilder: NotificationCompat.Builder,
                                    downloadNotification: DownloadNotification,
                                    context: Context) {
        val smallIcon = if (downloadNotification.isDownloading) {
            android.R.drawable.stat_sys_download
        } else {
            android.R.drawable.stat_sys_download_done
        }

        notificationBuilder.setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setSmallIcon(smallIcon)
            .setContentTitle(downloadNotification.title)
            .setContentText(getSubtitleText(context, downloadNotification))
            .setOngoing(downloadNotification.isOnGoingNotification)
            .setGroup(downloadNotification.groupId.toString())
            .setGroupSummary(false)
        if (downloadNotification.isFailed || downloadNotification.isCompleted) {
            notificationBuilder.setProgress(0, 0, false)
        } else {
            val progressIndeterminate = downloadNotification.progressIndeterminate
            val maxProgress = if (downloadNotification.progressIndeterminate) 0 else 100
            val progress = if (downloadNotification.progress < 0) 0 else downloadNotification.progress
            notificationBuilder.setProgress(maxProgress, progress, progressIndeterminate)
        }
        when {
            downloadNotification.isDownloading -> {
                notificationBuilder.setTimeoutAfter(getNotificationTimeOutMillis())
                    // Pause button hidden
                    /*
                    .addAction(R.drawable.fetch_notification_pause,
                        context.getString(R.string.fetch_notification_download_pause),
                        getActionPendingIntent(downloadNotification, DownloadNotification.ActionType.PAUSE))
                     */
                    .addAction(Fetch2R.drawable.fetch_notification_cancel,
                        context.getString(R.string.fetch_notification_download_cancel),
                        getActionPendingIntent(downloadNotification, DownloadNotification.ActionType.CANCEL))
            }
            downloadNotification.isPaused -> {
                notificationBuilder.setTimeoutAfter(getNotificationTimeOutMillis())
                    .addAction(Fetch2R.drawable.fetch_notification_cancel,
                        context.getString(R.string.fetch_notification_download_resume),
                        getActionPendingIntent(downloadNotification, DownloadNotification.ActionType.RESUME))
                    .addAction(Fetch2R.drawable.fetch_notification_cancel,
                        context.getString(R.string.fetch_notification_download_cancel),
                        getActionPendingIntent(downloadNotification, DownloadNotification.ActionType.CANCEL))
            }
            downloadNotification.isQueued -> {
                notificationBuilder.setTimeoutAfter(getNotificationTimeOutMillis())
            }
            else -> {
                notificationBuilder.setTimeoutAfter(DEFAULT_NOTIFICATION_TIMEOUT_AFTER_RESET)
            }
        }
    }

    override fun getActionPendingIntent(downloadNotification: DownloadNotification,
                                        actionType: DownloadNotification.ActionType): PendingIntent {

        val intent = Intent(notificationManagerAction)
        intent.putExtra(EXTRA_NAMESPACE, downloadNotification.namespace)
        intent.putExtra(EXTRA_DOWNLOAD_ID, downloadNotification.notificationId)
        intent.putExtra(EXTRA_NOTIFICATION_ID, downloadNotification.notificationId)
        intent.putExtra(EXTRA_GROUP_ACTION, false)
        intent.putExtra(EXTRA_NOTIFICATION_GROUP_ID, downloadNotification.groupId)
        val action = when (actionType) {
            DownloadNotification.ActionType.CANCEL -> ACTION_TYPE_CANCEL
            DownloadNotification.ActionType.DELETE -> ACTION_TYPE_DELETE
            DownloadNotification.ActionType.RESUME -> ACTION_TYPE_RESUME
            DownloadNotification.ActionType.PAUSE -> ACTION_TYPE_PAUSE
            DownloadNotification.ActionType.RETRY -> ACTION_TYPE_RETRY
            else -> ACTION_TYPE_INVALID
        }
        intent.putExtra(EXTRA_ACTION_TYPE, action)
        val flags = (if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0) or PendingIntent.FLAG_UPDATE_CURRENT
        return PendingIntent.getBroadcast(context, downloadNotification.notificationId + action, intent, flags)
    }

    override fun getDownloadNotificationTitle(download: Download): String {
        return Uri.parse(download.url).lastPathSegment ?: download.url
    }
}