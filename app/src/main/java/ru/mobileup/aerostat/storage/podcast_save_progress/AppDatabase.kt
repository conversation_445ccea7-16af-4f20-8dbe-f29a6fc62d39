package ru.mobileup.aerostat.storage.podcast_save_progress

import androidx.room.Database
import androidx.room.RoomDatabase

@Database(entities = [PodcastTimeListeningProgress::class], version = 1)
abstract class AppDatabase : RoomDatabase() {

    companion object {
        const val DATABASE_NAME = "appDatabase"
    }

    abstract fun podcastTimeListeningProgressDao(): PodcastTimeListeningProgressDao
}