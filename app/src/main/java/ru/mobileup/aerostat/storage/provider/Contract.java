package ru.mobileup.aerostat.storage.provider;

import android.database.Cursor;
import android.net.Uri;
import android.provider.BaseColumns;

import ru.mobileup.aerostat.api.model.ShowRelease;

/**
 * Created by terrakok on 05.11.14.
 */
public class Contract {
    private static final String TAG = "Contract";

    private Contract() {
    }

    public static final String AUTHORITY = "ru.mobileup.aerostat.storage.provider.AerostatContentProvider";

    static final String SCHEME = "content://";

    // Convenience prefixes
    private static final String CONTENT_URI_PREFIX = SCHEME + AUTHORITY + "/";

    private static final String CONTENT_TYPE_PREFIX = "vnd.android.cursor.dir/vnd."
            + AUTHORITY + ".";
    private static final String CONTENT_ITEM_TYPE_PREFIX = "vnd.android.cursor.item/vnd."
            + AUTHORITY + ".";

    // Common path parts
    private static final String PATH_ID = "id";

    /**
     * Posts contract
     */
    public static final class ShowReleaseTable implements BaseColumns {

        // This class cannot be instantiated
        private ShowReleaseTable() {
        }

        // region Table specific
        static final String TABLE_NAME = "shows";

        public static final String SHOW_NUMBER = "show_number";
        public static final String SHOW_NAME = "show_name";
        public static final String SHOW_NAME_SEARCH = "show_name_srch";
        public static final String SHOW_SUBTITLE = "show_subtitle";
        public static final String SHOW_DESCRIPTION = "show_description";
        public static final String SHOW_DESCRIPTION_SEARCH = "show_description_srch";
        public static final String SHOW_FAVORITE = "show_favorite";
        public static final String SHOW_FILE_URL = "show_file_url";
        public static final String SHOW_SAVED_STATUS = "show_saved_status";
        public static final String SHOW_DOWNLOADING_INDEX = "show_downloading_index";
        public static final String SHOW_SAVED_FILE_PATH = "show_saved_file_path";
        public static final String SHOW_CUE_URL = "show_cue_url";
        public static final String SHOW_LISTENING_PROGRESS = "show_listening_progress";
        // endregion

        /**
         * Used in search results list.
         * Helps to determine how many items is from {@link #SHOW_NAME}
         */
        public static final String FOUND_IN_NAMES_COUNT = "found_in_name_count";

        /**
         * Creates sql string for determining the count of names matching 'filter'
         */
        public static String sqlFoundInNamesCount(String filter) {
            return "(SELECT count(*) FROM " + TABLE_NAME + " WHERE " + SHOW_NAME_SEARCH + " LIKE '%" + filter + "%')";
        }

        // region Provider specific
        static final String PATH = "shows";

        public static final Uri CONTENT_URI = Uri.parse(CONTENT_URI_PREFIX + PATH);

        static final String CONTENT_TYPE = CONTENT_TYPE_PREFIX + PATH;
        static final String CONTENT_ITEM_TYPE = CONTENT_ITEM_TYPE_PREFIX + PATH;

        static final String DEFAULT_SORT_ORDER = SHOW_NUMBER + " DESC";

        public static Uri buildShowUri(int showNumber) {
            return CONTENT_URI.buildUpon().appendPath(PATH_ID).appendPath("" + showNumber).build();
        }

        static String getShowNumber(Uri uri) {
            return uri.getPathSegments().get(2);
        }
        // endregion

        public static ShowRelease getShowInfo(Cursor data) {

            return new ShowRelease(
                    data.getInt(data.getColumnIndex(SHOW_NUMBER)),
                    data.getString(data.getColumnIndex(SHOW_NAME)),
                    data.getString(data.getColumnIndex(SHOW_SUBTITLE)),
                    data.getString(data.getColumnIndex(SHOW_DESCRIPTION)),
                    data.getString(data.getColumnIndex(SHOW_FILE_URL)),
                    data.getString(data.getColumnIndex(SHOW_CUE_URL)),
                    data.getInt(data.getColumnIndex(SHOW_FAVORITE)) > 0,
                    data.getInt(data.getColumnIndex(SHOW_SAVED_STATUS)),
                    Long.parseLong(data.getString(data.getColumnIndex(SHOW_DOWNLOADING_INDEX))),
                    data.getString(data.getColumnIndex(SHOW_SAVED_FILE_PATH)),
                    data.getFloat(data.getColumnIndex(SHOW_LISTENING_PROGRESS))
            );
        }
    }
}
