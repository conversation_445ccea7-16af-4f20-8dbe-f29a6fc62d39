package ru.mobileup.aerostat.storage.provider;

import android.content.ContentProvider;
import android.content.ContentProviderClient;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.DatabaseUtils;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.net.Uri;
import android.os.Environment;
import android.text.TextUtils;

import java.io.File;
import java.text.DecimalFormat;
import java.text.NumberFormat;

import ru.mobileup.aerostat.api.model.ShowRelease;
import ru.mobileup.aerostat.util.Logger;

/**
 * Created by terrakok on 16.01.15.
 */
public class AerostatContentProvider extends ContentProvider {
    private static final String TAG = "AerostatContentProvider";

    private DatabaseHelper mDatabaseHelper;

    private static final UriMatcher sUriMatcher;

    // Uri matcher constants
    private static final int SHOWS = 10;
    private static final int SHOWS_NUMBER = 11;

    // UriMatcher initialization
    static {
        sUriMatcher = new UriMatcher(UriMatcher.NO_MATCH);
        final String idPattern = "/id/*";
        final String internalIdPattern = "/#";

        sUriMatcher.addURI(Contract.AUTHORITY, Contract.ShowReleaseTable.PATH, SHOWS);
        sUriMatcher.addURI(Contract.AUTHORITY, Contract.ShowReleaseTable.PATH + idPattern, SHOWS_NUMBER);
    }

    public static int performBulkUpdate(Context context, Uri uri, ContentValues[] values) {
        ContentResolver resolver = context.getContentResolver();
        try (ContentProviderClient client = resolver.acquireContentProviderClient(Contract.ShowReleaseTable.CONTENT_URI)) {
            if (client != null) {
                ContentProvider provider = client.getLocalContentProvider();
                if (provider instanceof AerostatContentProvider) {
                    AerostatContentProvider aerostatProvider = (AerostatContentProvider) provider;
                    return aerostatProvider.bulkUpdate(uri, values);
                }
            }
        } catch (Exception e) {
            Logger.e("AerostatContentProvider", "Failed to perform optimized bulkUpdate", e);
        }

        // Fallback: если не удалось вызвать bulkUpdate
        Logger.w("AerostatContentProvider", "Fallback to standard update");
        int updateCount = 0;
        for (ContentValues value : values) {
            updateCount += resolver.update(uri, value, null, null);
        }
        return updateCount;
    }

    @Override
    public boolean onCreate() {
        Logger.v(TAG, "onCreate");
        mDatabaseHelper = new DatabaseHelper(getContext());
        return true;
    }

    @Override
    public Cursor query(Uri uri, String[] projection, String selection, String[] selectionArgs, String sortOrder) {
        Logger.v(TAG, "query, " + uri.toString());

        String tableName;
        String id;
        Uri notifyUri = uri; // Ok for all except joined tables
        String tab; // Used only in join tables

        // проверяем Uri
        switch (sUriMatcher.match(uri)) {
            case SHOWS: // общий Uri
                tableName = Contract.ShowReleaseTable.TABLE_NAME;
                // если сортировка не указана, ставим свою
                if (TextUtils.isEmpty(sortOrder)) {
                    sortOrder = Contract.ShowReleaseTable.DEFAULT_SORT_ORDER;
                }
                Logger.v(TAG, "SHOWS");
                break;

            case SHOWS_NUMBER:
                tableName = Contract.ShowReleaseTable.TABLE_NAME;
                // добавляем internal ID к условию выборки
                id = Contract.ShowReleaseTable.getShowNumber(uri);
                selection = concatSelections(selection, Contract.ShowReleaseTable.SHOW_NUMBER + " = '" + id + "'");
                Logger.v(TAG, "SHOW_NUMBER, " + id);
                break;

            default:
                throw new IllegalArgumentException("Wrong URI: " + uri);
        }

        SQLiteDatabase db = mDatabaseHelper.getReadableDatabase();
        Cursor cursor = db.query(
                tableName,
                projection,
                selection,
                selectionArgs,
                null,
                null,
                sortOrder
        );

        // просим ContentResolver уведомлять этот курсор об изменениях данных в notifyUri
        cursor.setNotificationUri(getContext().getContentResolver(), notifyUri);
        return cursor;
    }

    private String concatSelections(String selection, String additionalSelection) {
        return (TextUtils.isEmpty(selection)) ? additionalSelection :
                selection + " AND " + additionalSelection;
    }

    @Override
    public String getType(Uri uri) {
        Logger.v(TAG, "getType, " + uri.toString());

        switch (sUriMatcher.match(uri)) {

            case SHOWS:
                return Contract.ShowReleaseTable.CONTENT_TYPE;
            case SHOWS_NUMBER:
                return Contract.ShowReleaseTable.CONTENT_ITEM_TYPE;

            default:
                throw new IllegalArgumentException("Wrong URI: " + uri);
        }
    }

    @Override
    public Uri insert(Uri uri, ContentValues contentValues) {
        Logger.v(TAG, "insert, " + uri.toString());

        String tableName;
        Uri notifyUri;

        switch (sUriMatcher.match(uri)) {

            case SHOWS:
                tableName = Contract.ShowReleaseTable.TABLE_NAME;
                notifyUri = Contract.ShowReleaseTable.CONTENT_URI;
                break;

            case SHOWS_NUMBER:
                tableName = Contract.ShowReleaseTable.TABLE_NAME;
                notifyUri = Contract.ShowReleaseTable.CONTENT_URI;
                break;

            default:
                throw new IllegalArgumentException("Wrong URI: " + uri);
        }

        SQLiteDatabase db = mDatabaseHelper.getWritableDatabase();

        long rowID = db.replaceOrThrow(
                tableName,
                null,
                contentValues); // Note: Here I use replace!

        notifyUri = ContentUris.withAppendedId(notifyUri, rowID);

        // уведомляем ContentResolver, что данные по адресу resultUri изменились
        getContext().getContentResolver().notifyChange(notifyUri, null);
        return notifyUri;
    }

    @Override
    public int delete(Uri uri, String selection, String[] selectionArgs) {
        Logger.v(TAG, "delete, " + uri.toString());

        String tableName;
        String id;

        switch (sUriMatcher.match(uri)) {

            case SHOWS:
                tableName = Contract.ShowReleaseTable.TABLE_NAME;
                Logger.v(TAG, "SHOWS");
                break;

            case SHOWS_NUMBER:
                tableName = Contract.ShowReleaseTable.TABLE_NAME;
                id = Contract.ShowReleaseTable.getShowNumber(uri);
                selection = concatSelections(selection, Contract.ShowReleaseTable.SHOW_NUMBER + " = '" + id + "'");
                Logger.v(TAG, "SHOW_NUMBER, " + id);
                break;

            default:
                throw new IllegalArgumentException("Wrong URI: " + uri);
        }

        SQLiteDatabase db = mDatabaseHelper.getWritableDatabase();

        int count = db.delete(
                tableName,
                selection,
                selectionArgs
        );

        getContext().getContentResolver().notifyChange(uri, null);
        return count;
    }

    @Override
    public int update(Uri uri, ContentValues values, String selection, String[] selectionArgs) {
        Logger.v(TAG, "update, " + uri.toString());

        String tableName;
        String id;

        switch (sUriMatcher.match(uri)) {

            case SHOWS:
                tableName = Contract.ShowReleaseTable.TABLE_NAME;
                Logger.v(TAG, "SHOWS");
                break;

            case SHOWS_NUMBER:
                tableName = Contract.ShowReleaseTable.TABLE_NAME;
                id = Contract.ShowReleaseTable.getShowNumber(uri);
                selection = concatSelections(selection, Contract.ShowReleaseTable.SHOW_NUMBER + " = '" + id + "'");
                Logger.v(TAG, "SHOW_NUMBER, " + id);
                break;

            default:
                throw new IllegalArgumentException("Wrong URI: " + uri);
        }

        SQLiteDatabase db = mDatabaseHelper.getWritableDatabase();

        int count = db.update(
                tableName,
                values,
                selection,
                selectionArgs);

        getContext().getContentResolver().notifyChange(uri, null);
        return count;
    }

    @Override
    public int bulkInsert(Uri uri, ContentValues[] values) {
        Logger.v(TAG, "bulkInsert start");
        int insertCount = 0;
        String tableName;

        final SQLiteDatabase db = mDatabaseHelper.getWritableDatabase();

        switch (sUriMatcher.match(uri)) {

            case SHOWS:
                tableName = Contract.ShowReleaseTable.TABLE_NAME;
                Logger.v(TAG, "SHOWS");
                break;

            default:
                throw new IllegalArgumentException("Wrong URI: " + uri);
        }

        try {
            db.beginTransaction();
            for (ContentValues value : values) {
                //пропустить вставку, если конфликт (то есть такой выпуск уже есть)
                long id = db.insertWithOnConflict(tableName, null, value, SQLiteDatabase.CONFLICT_IGNORE);
                if (id > 0) insertCount++;
            }
            db.setTransactionSuccessful();
        } finally {
            db.endTransaction();
        }
        Logger.v(TAG, "bulkInsert done, count = " + insertCount);

        getContext().getContentResolver().notifyChange(uri, null);

        return insertCount;
    }

    /**
     * Выполняет массовое обновление записей в одной транзакции для оптимизации производительности.
     * Каждый элемент массива должен содержать номер выпуска для идентификации записи.
     */
    public int bulkUpdate(Uri uri, ContentValues[] values) {
        Logger.v(TAG, "bulkUpdate start, count = " + values.length);
        int updateCount = 0;
        String tableName;

        final SQLiteDatabase db = mDatabaseHelper.getWritableDatabase();

        switch (sUriMatcher.match(uri)) {
            case SHOWS:
                tableName = Contract.ShowReleaseTable.TABLE_NAME;
                Logger.v(TAG, "SHOWS");
                break;

            default:
                throw new IllegalArgumentException("Wrong URI: " + uri);
        }

        try {
            db.beginTransaction();
            for (ContentValues value : values) {
                // Получаем номер выпуска для WHERE условия
                Integer showNumber = value.getAsInteger(Contract.ShowReleaseTable.SHOW_NUMBER);
                if (showNumber != null) {
                    String whereClause = Contract.ShowReleaseTable.SHOW_NUMBER + " = ?";
                    String[] whereArgs = {String.valueOf(showNumber)};

                    int updated = db.update(tableName, value, whereClause, whereArgs);
                    updateCount += updated;
                }
            }
            db.setTransactionSuccessful();
        } finally {
            db.endTransaction();
        }
        Logger.v(TAG, "bulkUpdate done, count = " + updateCount);

        getContext().getContentResolver().notifyChange(uri, null);

        return updateCount;
    }

    /**
     * Helps to create db.
     */
    public static class DatabaseHelper extends SQLiteOpenHelper {

        private static final String TAG = "DbOpenHelper";

        // DB
        static final String DB_NAME = "aerostat.db";
        static final int DB_VERSION = 10;

        // Types
        private static final String TYPE_INTEGER_PRIMARY_KEY = " INTEGER PRIMARY KEY"; // Note: will autoincrement without AUTOINCREMENT (slows)
        private static final String TYPE_TEXT = " TEXT";
        private static final String TYPE_INTEGER = " INTEGER";
        private static final String TYPE_REAL = " REAL";
        // Additional
        private static final String START_TAG = " (";
        private static final String STOP_TAG = ");";
        private static final String UNIQUE = " UNIQUE"; // Used for unique indexes
        private static final String NOT_NULL = " NOT NULL";
        private static final String COMMA = ", ";
        // For upgrading
        public static final String ALTER_TABLE = "ALTER TABLE ";
        public static final String ADD_COLUMN = " ADD COLUMN ";

        // region CREATE TABLE SQLs
        private static final String SQL_CREATE_TABLE_SHOWS = "CREATE TABLE " + Contract.ShowReleaseTable.TABLE_NAME
                + START_TAG
                + Contract.ShowReleaseTable._ID + TYPE_INTEGER_PRIMARY_KEY + COMMA // Inner id
                + Contract.ShowReleaseTable.SHOW_NUMBER + TYPE_INTEGER + UNIQUE + NOT_NULL + COMMA
                + Contract.ShowReleaseTable.SHOW_NAME + TYPE_TEXT + NOT_NULL + COMMA
                + Contract.ShowReleaseTable.SHOW_NAME_SEARCH + TYPE_TEXT + NOT_NULL + COMMA
                + Contract.ShowReleaseTable.SHOW_SUBTITLE + TYPE_TEXT + NOT_NULL + COMMA
                + Contract.ShowReleaseTable.SHOW_DESCRIPTION + TYPE_TEXT + NOT_NULL + COMMA
                + Contract.ShowReleaseTable.SHOW_DESCRIPTION_SEARCH + TYPE_TEXT + NOT_NULL + COMMA
                + Contract.ShowReleaseTable.SHOW_FILE_URL + TYPE_TEXT + NOT_NULL + COMMA
                + Contract.ShowReleaseTable.SHOW_CUE_URL + TYPE_TEXT  + COMMA
                + Contract.ShowReleaseTable.SHOW_FAVORITE + TYPE_INTEGER + NOT_NULL + COMMA
                + Contract.ShowReleaseTable.SHOW_SAVED_STATUS + TYPE_INTEGER + NOT_NULL + COMMA
                + Contract.ShowReleaseTable.SHOW_DOWNLOADING_INDEX + TYPE_TEXT + NOT_NULL + COMMA
                + Contract.ShowReleaseTable.SHOW_SAVED_FILE_PATH + TYPE_TEXT + COMMA
                + Contract.ShowReleaseTable.SHOW_LISTENING_PROGRESS + TYPE_REAL
                + STOP_TAG;
        // endregion

        // region DELETE TABLE SQLs
        private static final String SQL_DELETE_TABLE_SHOWS =
                "DROP TABLE IF EXISTS " + Contract.ShowReleaseTable.TABLE_NAME;
        // endregion

        public DatabaseHelper(Context context) {
            super(context, DB_NAME, null, DB_VERSION);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            db.execSQL(SQL_CREATE_TABLE_SHOWS);
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            Logger.w(TAG, "Database will be update from version " + oldVersion + " to version " + newVersion);

            if (newVersion < 4) {
                dropAndRecreateTables(db);
            } else {
                // For version 4 and above
                // Sequentially update db
                int upgradeTo = oldVersion + 1;
                while (upgradeTo <= newVersion) {
                    switch (upgradeTo) {
                        case 4:
                            updateDbTo4(db);
                            break;
                        case 5:
                            updateDbTo5(db);
                            break;
                        case 6:
                            updateDbTo6(db);
                            break;
                        case 7:
                            updateDbTo7(db);
                            break;
                        case 8:
                            updateDbTo8(db);
                            break;
                        case 9:
                            updateDbTo9(db);
                            break;
                        case 10:
                            updateDbTo10(db);
                            break;
                    }
                    upgradeTo++;
                }
            }

            Logger.w(TAG, "Database update completed.");
        }

        private void updateDbTo6(SQLiteDatabase db) {
            //перешли на Parse, надо поменять все ссылки на аудио файл по схеме: http://aerostats.ngapp.ru/music/001.mp3
            final String newUrl = "http://aerostats.ngapp.ru/music/";
            final NumberFormat numberFormat = new DecimalFormat("000");
            final String extension = ".mp3";
            Cursor cursor = db.query(
                    Contract.ShowReleaseTable.TABLE_NAME,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
            );

            ContentValues[] values = new ContentValues[cursor.getCount()];
            if (cursor.moveToFirst()) {
                ContentValues cv;
                do {
                    cv = new ContentValues();
                    DatabaseUtils.cursorRowToContentValues(cursor, cv);

                    int showNumber = cv.getAsInteger(Contract.ShowReleaseTable.SHOW_NUMBER);
                    String newUrlValue = newUrl + numberFormat.format(showNumber) + extension;
                    cv.put(Contract.ShowReleaseTable.SHOW_FILE_URL, newUrlValue);

                    //это добавлено, так как была бага в методе updateDbTo4() и значение newSearchValue не записывалось в базу
                    String newSearchValue = cv.getAsString(Contract.ShowReleaseTable.SHOW_NUMBER) + " " + cv.getAsString(Contract.ShowReleaseTable.SHOW_NAME).toUpperCase();
                    cv.put(Contract.ShowReleaseTable.SHOW_NAME_SEARCH, newSearchValue);

                    values[cursor.getPosition()] = cv;
                } while (cursor.moveToNext());
            }
            cursor.close();

            int updateCounter = 0;
            for (ContentValues value : values) {
                long id = db.insertWithOnConflict(Contract.ShowReleaseTable.TABLE_NAME, null, value, SQLiteDatabase.CONFLICT_REPLACE);
                if (id > 0) updateCounter++;
            }
            Logger.w(TAG, "Database updateCounter = " + updateCounter);
        }

        private void updateDbTo5(SQLiteDatabase db) {
            // Add new column for saved paths
            db.execSQL(ALTER_TABLE + Contract.ShowReleaseTable.TABLE_NAME +
                    ADD_COLUMN + Contract.ShowReleaseTable.SHOW_SAVED_FILE_PATH + TYPE_TEXT);

            // Fill with previous default paths
            String[] projection = {Contract.ShowReleaseTable.SHOW_FILE_URL, Contract.ShowReleaseTable._ID};
            String selection = Contract.ShowReleaseTable.SHOW_SAVED_STATUS + " = " + ShowRelease.STATUS_SAVED;

            Cursor cursor = db.query(Contract.ShowReleaseTable.TABLE_NAME,
                    projection,
                    selection,
                    null,
                    null,
                    null,
                    null);

            if (cursor.moveToFirst()) {
                do {
                    String fileUrl = cursor.getString(cursor.getColumnIndex(Contract.ShowReleaseTable.SHOW_FILE_URL));
                    String fileName = Uri.parse(fileUrl).getLastPathSegment();
                    String savedFilePath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC) + File.separator + "AerostatAudio" + File.separator + fileName;

                    ContentValues cv = new ContentValues(1);
                    cv.put(Contract.ShowReleaseTable.SHOW_SAVED_FILE_PATH, savedFilePath);

                    long id = cursor.getLong(cursor.getColumnIndex(Contract.ShowReleaseTable._ID));
                    String whereClause = Contract.ShowReleaseTable._ID + " = " + id;

                    db.update(Contract.ShowReleaseTable.TABLE_NAME, cv, whereClause, null);
                } while (cursor.moveToNext());
            }
            cursor.close();
        }

        private void updateDbTo4(SQLiteDatabase db) {
            //надо изменить данные в поле Contract.ShowReleaseTable.SHOW_NAME_SEARCH, добавив номер выпуска, чтобы по нему тоже искать
            Cursor cursor = db.query(
                    Contract.ShowReleaseTable.TABLE_NAME,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
            );

            ContentValues[] values = new ContentValues[cursor.getCount()];
            if (cursor.moveToFirst()) {
                ContentValues cv;
                do {
                    cv = new ContentValues();
                    DatabaseUtils.cursorRowToContentValues(cursor, cv);

                    String newSearchValue = cv.getAsString(Contract.ShowReleaseTable.SHOW_NUMBER) + " " + cv.getAsString(Contract.ShowReleaseTable.SHOW_NAME).toUpperCase();
                    cv.put(Contract.ShowReleaseTable.SHOW_NAME_SEARCH, newSearchValue);

                    values[cursor.getPosition()] = cv;
                } while (cursor.moveToNext());
            }
            cursor.close();

            int updateCounter = 0;
            for (ContentValues value : values) {
                long id = db.insertWithOnConflict(Contract.ShowReleaseTable.TABLE_NAME, null, value, SQLiteDatabase.CONFLICT_REPLACE);
                if (id > 0) updateCounter++;
            }
            Logger.w(TAG, "Database updateCounter = " + updateCounter);
        }

        private void dropAndRecreateTables(SQLiteDatabase db) {
            // Just drop tables
            Logger.w(TAG, "Database will be dropped!");
            db.execSQL(SQL_DELETE_TABLE_SHOWS);
            onCreate(db);
        }

        private void updateDbTo7(SQLiteDatabase db) {
            // Add new column for description search
            db.execSQL(ALTER_TABLE + Contract.ShowReleaseTable.TABLE_NAME +
                    ADD_COLUMN + Contract.ShowReleaseTable.SHOW_DESCRIPTION_SEARCH + TYPE_TEXT);

            // Fill with the existing uppercase descriptions
            String[] projection = {Contract.ShowReleaseTable.SHOW_DESCRIPTION, Contract.ShowReleaseTable._ID};

            Cursor cursor = db.query(Contract.ShowReleaseTable.TABLE_NAME,
                    projection,
                    null,
                    null,
                    null,
                    null,
                    null);

            if (cursor.moveToFirst()) {
                do {
                    String description = cursor.getString(cursor.getColumnIndex(Contract.ShowReleaseTable.SHOW_DESCRIPTION));

                    ContentValues cv = new ContentValues(1);
                    cv.put(Contract.ShowReleaseTable.SHOW_DESCRIPTION_SEARCH, description.toUpperCase());

                    long id = cursor.getLong(cursor.getColumnIndex(Contract.ShowReleaseTable._ID));
                    String whereClause = Contract.ShowReleaseTable._ID + " = " + id;

                    db.update(Contract.ShowReleaseTable.TABLE_NAME, cv, whereClause, null);
                } while (cursor.moveToNext());
            }
            cursor.close();
        }

        private void updateDbTo8(SQLiteDatabase db) {
            // Add new column for cue url
            db.execSQL(ALTER_TABLE + Contract.ShowReleaseTable.TABLE_NAME +
                    ADD_COLUMN + Contract.ShowReleaseTable.SHOW_CUE_URL + TYPE_TEXT);

            // надо добавить данные в поле Contract.ShowReleaseTable.SHOW_CUE_URL

            final String newUrl = "http://aerostats.getmobileup.com/cue/";
            final NumberFormat numberFormat = new DecimalFormat("000");
            final String extension = ".cue";

            Cursor cursor = db.query(
                    Contract.ShowReleaseTable.TABLE_NAME,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
            );

            int updateCounter = 0;
            if (cursor.moveToFirst()) {
                do {
                    int showNumber = cursor.getInt(cursor.getColumnIndex(Contract.ShowReleaseTable.SHOW_NUMBER));
                    String urlValue = newUrl + numberFormat.format(showNumber) + extension;

                    ContentValues cv = new ContentValues(1);
                    cv.put(Contract.ShowReleaseTable.SHOW_CUE_URL, urlValue);

                    long id = cursor.getLong(cursor.getColumnIndex(Contract.ShowReleaseTable._ID));
                    String whereClause = Contract.ShowReleaseTable._ID + " = " + id;

                    updateCounter += db.update(Contract.ShowReleaseTable.TABLE_NAME, cv, whereClause, null);

                } while (cursor.moveToNext());
            }
            cursor.close();

            Logger.w(TAG, "Database updateCounter = " + updateCounter);
        }

        private void updateDbTo9(SQLiteDatabase db) {
            // поменялся сервер, меняем все mp3 ссылки на http://aerostats.getmobileup.com/music/614.mp3
            final String newUrl = "http://aerostats.getmobileup.com/music/";
            final NumberFormat numberFormat = new DecimalFormat("000");
            final String extension = ".mp3";

            Cursor cursor = db.query(
                    Contract.ShowReleaseTable.TABLE_NAME,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
            );

            int updateCounter = 0;
            if (cursor.moveToFirst()) {
                do {
                    int showNumber = cursor.getInt(cursor.getColumnIndex(Contract.ShowReleaseTable.SHOW_NUMBER));
                    String urlValue = newUrl + numberFormat.format(showNumber) + extension;

                    ContentValues cv = new ContentValues(1);
                    cv.put(Contract.ShowReleaseTable.SHOW_FILE_URL, urlValue);

                    long id = cursor.getLong(cursor.getColumnIndex(Contract.ShowReleaseTable._ID));
                    String whereClause = Contract.ShowReleaseTable._ID + " = " + id;

                    updateCounter += db.update(Contract.ShowReleaseTable.TABLE_NAME, cv, whereClause, null);

                } while (cursor.moveToNext());
            }
            cursor.close();

            Logger.w(TAG, "Database updateCounter = " + updateCounter);
        }

        private void updateDbTo10(SQLiteDatabase db) {
            db.execSQL(ALTER_TABLE + Contract.ShowReleaseTable.TABLE_NAME +
                    ADD_COLUMN + Contract.ShowReleaseTable.SHOW_LISTENING_PROGRESS + TYPE_REAL);

            Cursor cursor = db.query(
                    Contract.ShowReleaseTable.TABLE_NAME,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null
            );

            int updateCounter = 0;
            if (cursor.moveToFirst()) {
                do {
                    ContentValues cv = new ContentValues(1);
                    cv.put(Contract.ShowReleaseTable.SHOW_LISTENING_PROGRESS, 0.0f);

                    long id = cursor.getLong(cursor.getColumnIndex(Contract.ShowReleaseTable._ID));
                    String whereClause = Contract.ShowReleaseTable._ID + " = " + id;

                    updateCounter += db.update(Contract.ShowReleaseTable.TABLE_NAME, cv, whereClause, null);

                } while (cursor.moveToNext());
            }
            cursor.close();

            Logger.w(TAG, "Database updateCounter = " + updateCounter);
        }
    }
}
