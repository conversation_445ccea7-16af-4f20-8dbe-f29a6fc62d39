package ru.mobileup.aerostat.storage.podcast_save_progress

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

@Dao
abstract class PodcastTimeListeningProgressDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract fun insert(podcastTimeListeningProgress: PodcastTimeListeningProgress)

    @Query("SELECT timeListeningProgress FROM podcastTimeListeningProgress WHERE podcastNumber = :podcastNumber")
    abstract fun getTimeListeningProgress(podcastNumber: Int): Int

}