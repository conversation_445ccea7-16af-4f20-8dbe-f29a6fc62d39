package ru.mobileup.aerostat.storage

import android.util.Log
import ru.mobileup.aerostat.util.Logger
import com.tonyodev.fetch2core.Logger as FetchLogger

class CrashlyticsFetchLogger : FetchLogger {

    override var enabled: Boolean = true

    override fun d(message: String) {
        Logger.logToCrashlytics(message)
    }

    override fun d(message: String, throwable: Throwable) {
        val stacktrace = Log.getStackTraceString(throwable)
        Logger.logToCrashlytics("$message\n$stacktrace")
    }

    override fun e(message: String) {
        Logger.logToCrashlytics(message)
    }

    override fun e(message: String, throwable: Throwable) {
        val stacktrace = Log.getStackTraceString(throwable)
        Logger.logToCrashlytics("$message\n$stacktrace")
    }
}