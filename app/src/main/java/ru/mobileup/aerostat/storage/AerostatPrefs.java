package ru.mobileup.aerostat.storage;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * Created by terrakok on 16.01.15.
 */
public class AerostatPrefs {
    private static final String TAG = "AerostatPrefs";

    // Default prefs
    private static final String PREFS_NAME_DEFAULT = "aerostat_prefs";
    private static final String KEY_MAX_SHOW_NUMBER = "key_nsnumber";
    private static final String KEY_DOWNLOAD_PATH = "key_download";
    private static final String KEY_LAST_PLAYED_NUMBER = "key_last_played_number";
    private static final String KEY_LAST_PLAYED_TIME = "key_last_played_time";
    private static final String KEY_SORT_PLAYLIST = "key_sort_playlist";
    private static final String KEY_CURRENT_PLAYLIST = "key_current_playlist";
    private static final String KEY_SHOWCASE_WAS_SHOWN = "key_showcase_was_shown";
    private static final String KEY_LAST_FORCE_RELOAD_TIME = "key_last_force_reload_time";

    // Интервал принудительной загрузки всех выпусков (1 неделя в миллисекундах)
    private static final long FORCE_RELOAD_INTERVAL_MS = 7 * 24 * 60 * 60 * 1000L;

    /**
     * Convenience method that returns prefs with default name
     *
     * @param context context
     * @return default prefs
     */
    private static SharedPreferences getPrefs(Context context) {
        return getNamedPrefs(context, PREFS_NAME_DEFAULT);
    }

    /**
     * Returns prefs with given name
     *
     * @param context   context
     * @param prefsName prefs name
     * @return prefs
     */
    private static SharedPreferences getNamedPrefs(Context context, String prefsName) {
        return context.getSharedPreferences(prefsName, Context.MODE_PRIVATE);
    }

    public static int getMaxShowNumber(Context context, int defaultValue) {
        return getPrefs(context).getInt(KEY_MAX_SHOW_NUMBER, defaultValue);
    }

    public static void putMaxShowNumber(Context context, int maxNumber) {
        getPrefs(context).edit().putInt(KEY_MAX_SHOW_NUMBER, maxNumber).apply();
    }

    public static String getDownloadPath(Context context, String defaultValue) {
        return getPrefs(context).getString(KEY_DOWNLOAD_PATH, defaultValue);
    }

    public static void putDownloadPath(Context context, String  shouldDownloadToExternal) {
        getPrefs(context).edit().putString(KEY_DOWNLOAD_PATH, shouldDownloadToExternal).apply();
    }

    /**
     * Returns last played show number and time
     * @param context context
     * @param defaultValue to return if there is no show number in prefs
     * @return saved lastPlayed or defaultValue if no show number found.
     */
    public static LastPlayed getLastPlayed(Context context, LastPlayed defaultValue) {
        SharedPreferences prefs = getPrefs(context);
        int showNumber = prefs.getInt(KEY_LAST_PLAYED_NUMBER, -1);
        int playedTime = prefs.getInt(KEY_LAST_PLAYED_TIME, 0);
        return showNumber == -1 ? defaultValue : new LastPlayed(showNumber, playedTime);
    }

    public static void putLastPlayed(Context context, LastPlayed lastPlayed) {
        getPrefs(context).edit()
                .putInt(KEY_LAST_PLAYED_NUMBER, lastPlayed != null ? lastPlayed.showNumber : -1)
                .putInt(KEY_LAST_PLAYED_TIME, lastPlayed != null ? lastPlayed.playedTime : 0)
                .apply();
    }

    public static boolean getSortPlaylist(Context context) {
        return getPrefs(context).getBoolean(KEY_SORT_PLAYLIST, false);
    }

    public static void toggleSortPlaylist(Context context) {
        putSortPlaylist(context, !getSortPlaylist(context));
    }

    public static void putSortPlaylist(Context context, boolean sortPlaylist) {
        getPrefs(context).edit()
                .putBoolean(KEY_SORT_PLAYLIST, sortPlaylist)
                .apply();
    }

    public static Playlist getCurrentPlaylist(Context context) {
        String playlistString = getPrefs(context).getString(KEY_CURRENT_PLAYLIST, Playlist.ALL.toString());
        return Playlist.valueOf(playlistString);
    }

    public static void putCurrentPlaylist(Context context, Playlist playlist) {
        getPrefs(context).edit()
                .putString(KEY_CURRENT_PLAYLIST, playlist.toString())
                .apply();
    }

    public static Boolean getShowcaseWasShown(Context context) {
        return getPrefs(context)
                .getBoolean(KEY_SHOWCASE_WAS_SHOWN, false);
    }

    public static void putShowcaseWasShown(Context context, Boolean isShow) {
        getPrefs(context).edit()
                .putBoolean(KEY_SHOWCASE_WAS_SHOWN, isShow)
                .apply();
    }

    /**
     * Получить время последней принудительной загрузки всех выпусков
     * @param context контекст
     * @return время в миллисекундах или 0 если принудительная загрузка еще не выполнялась
     */
    public static long getLastForceReloadTime(Context context) {
        return getPrefs(context).getLong(KEY_LAST_FORCE_RELOAD_TIME, 0);
    }

    /**
     * Сохранить время последней принудительной загрузки всех выпусков
     *
     * @param context контекст
     * @param timeMs  время в миллисекундах
     */
    public static void putLastForceReloadTime(Context context, long timeMs) {
        getPrefs(context).edit()
                .putLong(KEY_LAST_FORCE_RELOAD_TIME, timeMs)
                .apply();
    }

    /**
     * Проверить, нужна ли принудительная загрузка всех выпусков
     *
     * @param context контекст
     * @return true если прошло больше недели с последней принудительной загрузки
     */
    public static boolean shouldForceReloadAllShows(Context context) {
        long lastForceReloadTime = getLastForceReloadTime(context);
        long currentTime = System.currentTimeMillis();
        return lastForceReloadTime == 0 || (currentTime - lastForceReloadTime) >= FORCE_RELOAD_INTERVAL_MS;
    }

    /**
     * Holds the number and time of a last played show
     */
    public static class LastPlayed {
        public final int showNumber;
        public final int playedTime;

        public LastPlayed(int showNumber, int playedTime) {
            this.showNumber = showNumber;
            this.playedTime = playedTime;
        }
    }
}
