package ru.mobileup.aerostat.extension

import java.lang.reflect.Field
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

inline fun <reified R: Any, reified T> fieldByName(name: String) = object : ReadWriteProperty<R, T> {
    override fun getValue(thisRef: R, property: KProperty<*>): T {
        return thisRef.javaClass.field(name).get(thisRef) as T
    }

    override fun setValue(thisRef: R, property: KProperty<*>, value: T) {
        thisRef.javaClass.field(name).set(thisRef, value)
    }

    private fun Class<in R>.field(name: String): Field {
        return try {
            getDeclaredField(name).apply {
                isAccessible = true
            }
        } catch (e: NoSuchFieldException) {
            superclass!!.field(name)
        }
    }

}