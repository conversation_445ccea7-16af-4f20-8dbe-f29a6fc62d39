package ru.mobileup.aerostat.ui.main;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.IBinder;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.loader.app.LoaderManager;
import androidx.loader.content.CursorLoader;
import androidx.loader.content.Loader;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.android.material.navigation.NavigationView;

import java.util.ArrayList;
import java.util.List;

import hotchemi.android.rate.AppRate;
import ru.mobileup.aerostat.BuildConfig;
import ru.mobileup.aerostat.R;
import ru.mobileup.aerostat.api.model.CueTrackData;
import ru.mobileup.aerostat.api.model.ShowRelease;
import ru.mobileup.aerostat.player.PlayerManipulationInterface;
import ru.mobileup.aerostat.player.PodcastPlayerService;
import ru.mobileup.aerostat.storage.AerostatPrefs;
import ru.mobileup.aerostat.storage.DownloadManager;
import ru.mobileup.aerostat.storage.Playlist;
import ru.mobileup.aerostat.storage.provider.Contract;
import ru.mobileup.aerostat.ui.about.AboutActivity;
import ru.mobileup.aerostat.ui.common.BaseFragment;
import ru.mobileup.aerostat.ui.common.CurrentShowChangesListener;
import ru.mobileup.aerostat.ui.common.FilterableFragment;
import ru.mobileup.aerostat.ui.common.OnBackPressedListener;
import ru.mobileup.aerostat.ui.main.list.ShowsListFragment;
import ru.mobileup.aerostat.ui.main.show.ShowInfoFragment;
import ru.mobileup.aerostat.ui.main.show.ShowPlayListFragment;
import ru.mobileup.aerostat.ui.settings.SettingsActivity;
import ru.mobileup.aerostat.util.GAHelper;
import ru.mobileup.aerostat.util.InsetUtilsKt;
import ru.mobileup.aerostat.util.Logger;
import ru.mobileup.aerostat.util.ShowReleaseUtils;

public class MainActivity
        extends AppCompatActivity
        implements PodcastPlayerService.PodcastPlayerCallback,
        ToolbarModeSwitcher,
        SearchView.OnQueryTextListener,
        LoaderManager.LoaderCallbacks<Cursor>,
        NavigationView.OnNavigationItemSelectedListener {

    private static final String TAG = "MainActivity";
    private static final String BROADCAST_EXTRA_SHOW = "be_obj";
    private static final String BROADCAST_EXTRA_ACTION = "be_act";
    private static final String LOCAL_BROADCAST = "play_s_event";

    private static final String STATE_SEARCH = "m_state_search";

    private static final int ACTION_PLAY_SHOW = 0;
    private static final int ACTION_OPEN_INFO_SHOW = 1;

    private static final int PERMISSIONS_REQUEST_CODE = 200;
    private static final int CHOOSE_DOWNLOAD_DIRECTORY_REQUEST_CODE = 200;

    private int mToolbarMode = -1;

    //флаг, включающийся при изменении seek бара
    private boolean isSeekTracking = false;

    //переменные подключения к сервису плеера
    private Intent mPlayerServiceIntent;
    private ServiceConnection mPlayerServiceConnection;
    private PlayerManipulationInterface mPlayerManipulation;

    //вью плеера
    private View mManipulationPlayerLay;
    private TextView mManipulationPlayerCurrentTime;
    private TextView mManipulationPlayerFullTime;
    private TextView mManipulationPlayerTitle;
    private ImageView mManipulationPlayerPlayButton;
    private View mManipulationPlayerRewindButton;
    private View mManipulationPlayerForwardButton;
    private ProgressBar mManipulationPlayerProgress;
    private SeekBar mManipulationSeekBar;

    private Toolbar mToolbar;
    private SearchView mSearchView;
    private DrawerLayout mDrawerLayout;
    private NavigationView mNavigationView;

    //сохраненный поисковый запрос
    private String savedSearch;

    //шоу, которое в данный момент показывается в плеере
    //для связи с состояниями "в избранном" и "загружено" активити держит CursorLoader
    private ShowRelease mCurrentShowRelease;

    // шоу которое нужно скачать после того как выберут папку для скачивания
    private ShowRelease mPendingDownloadShowRelease;

    //колбэк по которому надо снимать выделение текста во фрагментах - иначе слайдер залезет на выделение
    public interface TextSelectionStateCallback {
        void clearTextSelection();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        InsetUtilsKt.enableEdgeToEdge(this);
        setContentView(R.layout.activity_main);

        initViews();
        setupPlayerViews();
        setupSeekBar();
        setupToolbar();
        initPlayerService();
        requestPermissions();

        mDrawerLayout.setStatusBarBackground(android.R.color.white);
        mNavigationView.setNavigationItemSelectedListener(this);

        if (savedInstanceState == null) {
            showPlaylistScreen(Playlist.ALL);

            mNavigationView.getMenu().findItem(R.id.action_shows).setChecked(true);
        }

        AppRate.with(this)
                .setInstallDays(5)
                .setLaunchTimes(5)
                .setRemindInterval(3)
                .setShowNeutralButton(true)
                .monitor();

        // Show a dialog if meets conditions
        AppRate.showRateDialogIfMeetsConditions(this);

        DownloadManager.INSTANCE.setActivity(this);

        mManipulationPlayerTitle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openPlayList();
            }
        });
    }

    /**
     * Find all activity views by ID's.
     * Init global values with view sizes.
     * Start initialization views.
     */
    private void initViews() {
        mToolbar = findViewById(R.id.main_toolbar);
        mDrawerLayout = findViewById(R.id.drawer_layout);
        mNavigationView = findViewById(R.id.drawer_navigation);

        mManipulationPlayerLay = findViewById(R.id.player_manipulation_view);
        mManipulationPlayerCurrentTime = findViewById(R.id.player_manipulation_current_time);
        mManipulationPlayerFullTime = findViewById(R.id.player_manipulation_full_time);
        mManipulationPlayerTitle = findViewById(R.id.player_manipulation_title);
        mManipulationPlayerPlayButton = findViewById(R.id.player_manipulation_play);
        mManipulationPlayerRewindButton = findViewById(R.id.player_manipulation_rewind);
        mManipulationPlayerForwardButton = findViewById(R.id.player_manipulation_forward);
        mManipulationPlayerProgress = findViewById(R.id.player_manipulation_prog);
        mManipulationSeekBar = findViewById(R.id.player_manipulation_seek);
    }

    /**
     * set listener for seekBar
     */
    private void setupSeekBar() {
        SeekBar.OnSeekBarChangeListener seekBarChangeListener = new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                updateSeekBarViewWithTrackData(null, progress, seekBar.getMax(), null);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                isSeekTracking = true;
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                isSeekTracking = false;
                if (mPlayerManipulation != null) {
                    mPlayerManipulation.setProgress(seekBar.getProgress());
                }
            }
        };
        mManipulationSeekBar.setOnSeekBarChangeListener(seekBarChangeListener);
    }

    /**
     * Set Toolbar as support action bar
     */
    private void setupToolbar() {
        setSupportActionBar(mToolbar);

        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
        switchToolbarMode(TOOLBAR_MODE_MAIN);
    }

    /**
     * Setup clicks on player view buttons
     */
    private void setupPlayerViews() {
        mManipulationPlayerPlayButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPlayerManipulation != null) {
                    mPlayerManipulation.pauseOrResumePlayer(mCurrentShowRelease);
                }
            }
        });
        mManipulationPlayerRewindButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPlayerManipulation != null) {
                    mPlayerManipulation.discreteRewind();
                }
            }
        });
        mManipulationPlayerForwardButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPlayerManipulation != null) {
                    mPlayerManipulation.discreteForward();
                }
            }
        });
    }

    private void showPlaylistScreen(Playlist playlist) {
        switch (playlist) {
            case ALL:
                showNewFragment(new ShowsListFragment(), false);
                break;
            case FAVORITES:
                showNewFragment(new FavoritesShowsListFragment(), false);
                break;
            case SAVED:
                showNewFragment(new SavedShowsListFragment(), false);
                break;
        }
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem menuItem) {
        switch (menuItem.getItemId()) {
            case R.id.action_shows:
                AerostatPrefs.putCurrentPlaylist(this, Playlist.ALL);
                showPlaylistScreen(Playlist.ALL);
                break;
            case R.id.action_favorites:
                AerostatPrefs.putCurrentPlaylist(this, Playlist.FAVORITES);
                showPlaylistScreen(Playlist.FAVORITES);
                break;
            case R.id.action_saved:
                AerostatPrefs.putCurrentPlaylist(this, Playlist.SAVED);
                showPlaylistScreen(Playlist.SAVED);
                break;
            case R.id.action_settings:
                showNewActivity(SettingsActivity.class);
                break;
            case R.id.action_about:
                showNewActivity(AboutActivity.class);
                break;
            case R.id.action_share:
                shareTextUrl();
                break;
            case R.id.action_feedback:
                sendFeedback();
                break;
        }

        closeNavigationDrawer();
        return true;
    }

    private void openNavigationDrawer() {
        mDrawerLayout.openDrawer(GravityCompat.START);
    }

    private void closeNavigationDrawer() {
        mDrawerLayout.closeDrawer(GravityCompat.START);
    }

    /**
     * Initialization PlayerServiceConnection (not connect)
     */
    private void initPlayerService() {
        mPlayerServiceIntent = new Intent(this, PodcastPlayerService.class);
        mPlayerServiceConnection = new ServiceConnection() {
            @Override
            public void onServiceConnected(ComponentName name, IBinder service) {
                Logger.i(TAG, "onServiceConnected");
                mPlayerManipulation = ((PodcastPlayerService.LocalBinder) service).getService();
                mPlayerManipulation.setListener(MainActivity.this);
            }

            @Override
            public void onServiceDisconnected(ComponentName name) {
                Logger.i(TAG, "onServiceDisconnected");
                mPlayerManipulation.setListener(null);
                preparePlayerView(null);
                mPlayerManipulation = null;
            }
        };
    }

    /**
     * Send new track to PlayerService for playing
     *
     * @param showRelease new show track
     */
    private void playShowRelease(ShowRelease showRelease) {
        if (mPlayerManipulation != null) {
            mPlayerManipulation.openShowRelease(showRelease, true, false);
        }

        GAHelper.getInstance().sendEventGA(
                getString(R.string.ga_category_user_action),
                getString(R.string.ga_event_click),
                getString(R.string.ga_label_play),
                Long.getLong(showRelease.getNumber() + ""),
                this
        );
    }

    private void openShowInfo(ShowRelease showRelease) {
        if (mCurrentShowRelease != null && mCurrentShowRelease.getNumber() == showRelease.getNumber()) {
            openCurrentShowInfo();
            return;
        }

        showNewFragment(ShowInfoFragment.getNewInstance(showRelease, false), true);
    }

    private void openCurrentShowInfo() {
        Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.container);

        if (mCurrentShowRelease == null || (currentFragment instanceof ShowInfoFragment
                && ((ShowInfoFragment) currentFragment).getShowRelease().getNumber() == mCurrentShowRelease.getNumber())) {
            return;
        }

        showNewFragment(ShowInfoFragment.getNewInstance(mCurrentShowRelease, true), true);
    }

    private void openPlayList() {
        Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.container);

        if (mCurrentShowRelease == null || (currentFragment instanceof ShowPlayListFragment
                && ((ShowPlayListFragment) currentFragment).getShowRelease().getNumber() == mCurrentShowRelease.getNumber())) {
            return;
        }

        showNewFragment(ShowPlayListFragment.getNewInstance(mCurrentShowRelease, true), true);
    }

    public void forceChooseDownloadDirectory(ShowRelease pendingDownloadShowRelease) {
        mPendingDownloadShowRelease = pendingDownloadShowRelease;
        Intent intent = new Intent(this, SettingsActivity.class);
        intent.putExtra(SettingsActivity.EXTRA_FORCE_CHOOSE_DOWNLOAD_DIRECTORY, true);
        startActivityForResult(intent, CHOOSE_DOWNLOAD_DIRECTORY_REQUEST_CODE);
    }

    private void registerLocalBroadcastReceiver() {
        LocalBroadcastManager.getInstance(this).registerReceiver(mMessageReceiver, new IntentFilter(LOCAL_BROADCAST));
    }

    private void unregisterLocalBroadcastReceiver() {
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mMessageReceiver);
    }

    @Override
    protected void onStart() {
        Logger.v(TAG, "onStart");
        super.onStart();
        preparePlayerView(null);//всегда сперва скрываем плеер
        bindService(mPlayerServiceIntent, mPlayerServiceConnection, BIND_AUTO_CREATE);
        registerLocalBroadcastReceiver();
    }

    @Override
    protected void onResume() {
        Logger.v(TAG, "onResume");
        super.onResume();
        restoreSearchQuery();
    }

    @Override
    protected void onPause() {
        Logger.v(TAG, "onPause");
        super.onPause();
        saveSearchQuery();
    }

    @Override
    protected void onStop() {
        Logger.v(TAG, "onStop");
        unregisterLocalBroadcastReceiver();
        unbindService(mPlayerServiceConnection);
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        DownloadManager.INSTANCE.resetActivity();
        super.onDestroy();
    }

    @Override
    public void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        Logger.v(TAG, "onRestoreInstanceState");
        savedSearch = savedInstanceState.getString(STATE_SEARCH);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        Logger.v(TAG, "onSaveInstanceState");
        outState.putString(STATE_SEARCH, savedSearch);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(requestCode == CHOOSE_DOWNLOAD_DIRECTORY_REQUEST_CODE) {
            if(resultCode == RESULT_OK && mPendingDownloadShowRelease != null) {
                ShowReleaseUtils.downloadShowOnDisk(mPendingDownloadShowRelease, this);
            }
            mPendingDownloadShowRelease = null;
        }
    }

    /**
     * Setup player view with showRelease data
     *
     * @param showRelease
     */
    private void preparePlayerView(final ShowRelease showRelease) {
        Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.container);
        if (currentFragment instanceof CurrentShowChangesListener) {
            ((CurrentShowChangesListener) currentFragment).onCurrentShowChange(showRelease);
        }

        if (showRelease != null && mPlayerManipulation != null) {
            startCursorLoaderForShowRelease(showRelease);

            mManipulationPlayerLay.setVisibility(View.VISIBLE);
        } else {
            mManipulationPlayerLay.setVisibility(View.GONE);
        }

    }

    private void updateTrackDataFields(ShowRelease showRelease, CueTrackData trackData) {
        if (showRelease != null) {
            if (trackData != null) {
                mManipulationPlayerTitle.setText(trackData.getPerformer() + " – " + trackData.getTitle());
            } else {
                mManipulationPlayerTitle.setText(getString(R.string.player_no_track_data_text));
            }
        }
    }

    /**
     * Init cursor loader for work with toolbar @TOOLBAR_MODE_PLAYER action menu states
     *
     * @param showRelease
     */
    private void startCursorLoaderForShowRelease(ShowRelease showRelease) {
        mCurrentShowRelease = showRelease;
        getSupportLoaderManager().restartLoader(0, null, this);
    }

    @Override
    public void onPlayerWait(ShowRelease showRelease) {
        mManipulationPlayerProgress.setVisibility(View.VISIBLE);
        mManipulationPlayerTitle.setText(null);
        preparePlayerView(showRelease);
        updateTrackDataFields(showRelease, null);
    }

    @Override
    public void onPlayerStart(ShowRelease showRelease) {
        mManipulationPlayerProgress.setVisibility(View.GONE);
        mManipulationPlayerPlayButton.setImageResource(R.drawable.ic_player_manipulation_pause);
        preparePlayerView(showRelease);
        restoreToolbarMode();
    }

    @Override
    public void onPlayerPause(ShowRelease showRelease) {
        mManipulationPlayerProgress.setVisibility(View.GONE);
        mManipulationPlayerPlayButton.setImageResource(R.drawable.ic_player_manipulation_play);
        preparePlayerView(showRelease);
    }

    @Override
    public void onPlayerStop(final ShowRelease showRelease) {
        mManipulationPlayerProgress.setVisibility(View.GONE);
        mManipulationPlayerPlayButton.setImageResource(R.drawable.ic_player_manipulation_play);
        preparePlayerView(showRelease);
    }

    @Override
    public void onPlayerRelease() {
        mManipulationSeekBar.setProgress(0);
        mManipulationPlayerProgress.setVisibility(View.GONE);
        mManipulationPlayerPlayButton.setImageResource(R.drawable.ic_player_manipulation_play);
        preparePlayerView(null);
    }

    @Override
    public void onPlayerError(ShowRelease showRelease) {
        mManipulationPlayerProgress.setVisibility(View.GONE);
        mManipulationPlayerPlayButton.setImageResource(R.drawable.ic_player_manipulation_play);
        preparePlayerView(showRelease);
        Toast.makeText(this, R.string.smth_error, Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onPlayerProgress(ShowRelease showRelease, int currentTime, int fullTime, CueTrackData trackData) {
        if (!isSeekTracking && fullTime != 0) {
            updateSeekBarViewWithTrackData(showRelease, currentTime, fullTime, trackData);
        }
    }

    @Override
    public void onPlayerFileNotExist(final ShowRelease showRelease) {
        if (isFinishing()) return;

        new AlertDialog.Builder(this)
                .setMessage(R.string.play_file_not_exists_msg)
                .setPositiveButton(
                        R.string.play_online_title,
                        new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                if (mPlayerManipulation != null) {
                                    mPlayerManipulation.openShowRelease(showRelease, true, true);
                                }
                                dialog.dismiss();
                            }
                        }
                )
                .setNegativeButton(R.string.common_close, null)
                .create().show();
    }

    private void updateSeekBarViewWithTrackData(ShowRelease showRelease, int currentTime, int fullTime, CueTrackData trackData) {
        updateTrackDataFields(showRelease, trackData);

        if (showRelease != null) {
            float progress = (float) (Math.round(((float) currentTime / (float) fullTime) * 10.0) / 10.0);
            if (showRelease.getListeningProgress() < progress) {
                ShowReleaseUtils.setListeningProgress(showRelease, progress, this);
            }
            ShowReleaseUtils.setListeningTime(showRelease, currentTime);
        }

        mManipulationSeekBar.setMax(fullTime);
        mManipulationSeekBar.setProgress(currentTime);

        mManipulationPlayerCurrentTime.setText(getStringTime(currentTime));
        mManipulationPlayerFullTime.setText(getStringTime(fullTime));
    }

    /**
     * Convert milliseconds to "xx:xx"
     *
     * @param time in milliseconds
     * @return "xx:xx" String
     */
    private String getStringTime(long time) {
        int minutes = (int) (time / 60000L);
        int secundes = ((int) (time / 1000L)) - minutes * 60;

        String result = "";

        if (minutes < 10) result += "0";
        result += minutes + ":";

        if (secundes < 10) result += "0";
        result += secundes;

        return result;
    }

    /**
     * Replace fragments in container if needed
     *
     * @param fragment   new fragment
     * @param addToStack flag for add to fragment back stack
     */
    private void showNewFragment(Fragment fragment, boolean addToStack) {
        FragmentManager fragmentManager = getSupportFragmentManager();

        Fragment currentFragment = fragmentManager.findFragmentById(R.id.container);
        if (currentFragment == null || !fragment.getClass().isInstance(currentFragment)) {
            Logger.v(TAG, "showNewFragment = " + ((Object) fragment).getClass().getSimpleName());

            FragmentTransaction fragmentTransaction = fragmentManager.beginTransaction();
            fragmentTransaction.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE);
            fragmentTransaction.replace(R.id.container, fragment);
            if (addToStack) {
                saveSearchQuery();
                fragmentTransaction.addToBackStack(null);
            }
            fragmentTransaction.commit();

            GAHelper.getInstance().sendEventGA(
                    getString(R.string.ga_category_user_action),
                    getString(R.string.ga_event_open_fragment),
                    ((Object) fragment).getClass().getSimpleName(),
                    null,
                    this
            );
        }
    }

    /**
     * Start new activity
     *
     * @param activityClass new activity
     */
    private void showNewActivity(Class<? extends Activity> activityClass) {
        startActivity(new Intent(this, activityClass));

        GAHelper.getInstance().sendEventGA(
                getString(R.string.ga_category_user_action),
                getString(R.string.ga_event_open_activity),
                activityClass.getSimpleName(),
                null,
                this
        );
    }

    /**
     * Force restore Toolbar title and mode
     */
    private void restoreToolbarMode() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        Fragment fragment = fragmentManager.findFragmentById(R.id.container);
        if (fragment != null && fragment instanceof BaseFragment) {
            setToolbarTitle(((BaseFragment) fragment).getTitle());
            setToolbarSubtitle(((BaseFragment) fragment).getSubtitle());
            switchToolbarMode(((BaseFragment) fragment).getToolbarMode());
        }
    }

    /**
     * Play new show method
     *
     * @param context
     * @param showRelease
     */
    public static void playShowBroadcastMessage(Context context, ShowRelease showRelease) {
        Logger.d(TAG, "playShowBroadcastMessage");

        Intent intent = new Intent(LOCAL_BROADCAST);
        intent.putExtra(BROADCAST_EXTRA_SHOW, showRelease);
        intent.putExtra(BROADCAST_EXTRA_ACTION, ACTION_PLAY_SHOW);
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }

    /**
     * Open show info fragment
     *
     * @param context
     * @param showRelease
     */
    public static void openShowInfoBroadcastMessage(Context context, ShowRelease showRelease) {
        Logger.d(TAG, "openShowInfoBroadcastMessage");

        Intent intent = new Intent(LOCAL_BROADCAST);
        intent.putExtra(BROADCAST_EXTRA_SHOW, showRelease);
        intent.putExtra(BROADCAST_EXTRA_ACTION, ACTION_OPEN_INFO_SHOW);
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }

    private BroadcastReceiver mMessageReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            // Get extra data included in the Intent
            ShowRelease showRelease = (ShowRelease) intent.getSerializableExtra(BROADCAST_EXTRA_SHOW);
            Logger.d(TAG, "Got new message: show №" + showRelease.getNumber());

            switch (intent.getIntExtra(BROADCAST_EXTRA_ACTION, ACTION_PLAY_SHOW)) {
                case ACTION_PLAY_SHOW:
                    playShowRelease(showRelease);
                    break;
                case ACTION_OPEN_INFO_SHOW:
                    openShowInfo(showRelease);
                    break;
            }
        }
    };

    @Override
    public void setToolbarTitle(String title) {
        getSupportActionBar().setTitle(title);
    }

    @Override
    public void setToolbarSubtitle(@Nullable String subtitle) {
        getSupportActionBar().setSubtitle(subtitle);
    }

    @Override
    public void switchToolbarMode(int mode) {
        if (mToolbarMode != mode) {
            Logger.d(TAG, "switchToolbarMode: " + mode);
            mToolbarMode = mode;
            switch (mode) {
                case TOOLBAR_MODE_MAIN:
                    mDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED);
                    getSupportActionBar().setHomeAsUpIndicator(R.drawable.ic_menu);
                    invalidateOptionsMenu();
                    break;
                case TOOLBAR_MODE_SIMPLE:
                    mDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
                    getSupportActionBar().setHomeAsUpIndicator(R.drawable.ic_arrow_back);
                    invalidateOptionsMenu();
                    break;
                case TOOLBAR_MODE_SHOW:
                    mDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
                    getSupportActionBar().setHomeAsUpIndicator(R.drawable.ic_arrow_back);
                    invalidateOptionsMenu();
                    break;
                case TOOLBAR_MODE_PLAYLIST:
                    mDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
                    getSupportActionBar().setHomeAsUpIndicator(R.drawable.ic_close);
                    invalidateOptionsMenu();
                    break;
            }
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        switch (mToolbarMode) {
            case TOOLBAR_MODE_MAIN:
                getMenuInflater().inflate(R.menu.main_menu, menu);
                mSearchView = (SearchView) menu.findItem(R.id.action_search).getActionView();
                mSearchView.setOnQueryTextListener(this);

                if (!TextUtils.isEmpty(savedSearch)) {
                    Logger.i(TAG, "savedSearch: " + savedSearch);
                    menu.findItem(R.id.action_search).expandActionView();
                    mSearchView.setQuery(savedSearch, true);
                }
                return true;
            case TOOLBAR_MODE_SIMPLE:
                return false;
            case TOOLBAR_MODE_SHOW:
                getMenuInflater().inflate(R.menu.show_menu, menu);
                return true;
        }
        return false;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.container);
        switch (menuItem.getItemId()) {
            case R.id.action_sort:
                toggleSort();
                return true;
            case android.R.id.home:
                if (mToolbarMode == TOOLBAR_MODE_MAIN) {
                    openNavigationDrawer();
                } else if (currentFragment instanceof OnBackPressedListener && ((OnBackPressedListener) currentFragment).onBackPressed()) {
                    // do nothing
                } else {
                    onBackPressed();
                }
                return true;
            default:
                return super.onOptionsItemSelected(menuItem);
        }
    }

    @Override
    public void onBackPressed() {
        Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.container);

        if (currentFragment instanceof OnBackPressedListener && ((OnBackPressedListener) currentFragment).onBackPressed()) {
            return;
        }

        //если развернуто поле поиска
        if (mSearchView != null && !mSearchView.isIconified()) {
            clearSearchResult();
            super.onBackPressed();
        } else {
            //если это не стартовый фрагмент
            FragmentManager fragmentManager = getSupportFragmentManager();
            if (fragmentManager.getBackStackEntryCount() == 0) {
                if ((currentFragment instanceof FavoritesShowsListFragment
                        || currentFragment instanceof SavedShowsListFragment
                )) {
                    showNewFragment(new ShowsListFragment(), false);
                    mNavigationView.getMenu().findItem(R.id.action_shows).setChecked(true);
                    return;
                }
            }
            super.onBackPressed();
        }
    }

    private void sendFeedback() {
        Intent intent = new Intent(Intent.ACTION_SENDTO, Uri.parse("mailto:" + getString(R.string.feedback_email)));
        intent.putExtra(Intent.EXTRA_EMAIL, new String[]{getString(R.string.feedback_email)});
        intent.putExtra(Intent.EXTRA_SUBJECT, getString(R.string.feedback_subject));
        intent.putExtra(Intent.EXTRA_TEXT, getString(
                R.string.feedback_text,
                BuildConfig.VERSION_NAME,
                BuildConfig.VERSION_CODE,
                Build.MANUFACTURER,
                Build.MODEL,
                Build.VERSION.RELEASE));

        startActivity(Intent.createChooser(intent, getString(R.string.feedback)));
    }

    private void shareTextUrl() {
        Intent share = new Intent(android.content.Intent.ACTION_SEND);
        share.setType("text/plain");

        share.putExtra(Intent.EXTRA_SUBJECT, getString(R.string.share_subject));
        share.putExtra(Intent.EXTRA_TEXT, getString(R.string.share_link));

        startActivity(Intent.createChooser(share, getString(R.string.share)));
    }

    private void toggleSort() {
        AerostatPrefs.toggleSortPlaylist(this);
        Fragment fragment = getSupportFragmentManager().findFragmentById(R.id.container);
        if (fragment instanceof LoaderManager.LoaderCallbacks) {
            LoaderManager.LoaderCallbacks loaderCallbacks = (LoaderManager.LoaderCallbacks) fragment;
            fragment.getLoaderManager().restartLoader(0, null, loaderCallbacks);
        }
    }

    @Override
    public boolean onQueryTextSubmit(String s) {
        return false;
    }

    @Override
    public boolean onQueryTextChange(String s) {
        Logger.i(TAG, "onQueryTextChange: " + s);
        Fragment fragment = getSupportFragmentManager().findFragmentById(R.id.container);
        if (fragment instanceof FilterableFragment) {
            ((FilterableFragment) fragment).filterData(s);
            return true;
        }
        return false;
    }

    private void saveSearchQuery() {
        Logger.i(TAG, "saveSearchQuery");
        Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.container);
        if (currentFragment instanceof FilterableFragment) {
            savedSearch = ((FilterableFragment) currentFragment).getFilter();
        }
    }

    private void restoreSearchQuery() {
        if (!TextUtils.isEmpty(savedSearch) && mToolbarMode == TOOLBAR_MODE_MAIN) {
            if (mSearchView != null) {
                mSearchView.setQuery(savedSearch, true);
                onQueryTextChange(savedSearch);
            }
        }
    }

    private void clearSearchResult() {
        Logger.i(TAG, "clearSearchResult");
        savedSearch = null;
        onQueryTextChange("");
    }

    private CursorLoader getShowReleaseCursorLoader(int showNumber) {
        return new CursorLoader(
                this,
                Contract.ShowReleaseTable.buildShowUri(showNumber),
                null,
                null,
                null,
                null
        );
    }

    @Override
    public Loader<Cursor> onCreateLoader(int id, Bundle args) {
        Logger.d(TAG, "onCreateLoader id = " + id);
        return getShowReleaseCursorLoader(mCurrentShowRelease.getNumber());
    }

    @Override
    public void onLoadFinished(Loader<Cursor> loader, Cursor data) {
        Logger.d(TAG, "onLoadFinished");
        if (data.moveToFirst()) {
            mCurrentShowRelease = Contract.ShowReleaseTable.getShowInfo(data);
        }
        invalidateOptionsMenu();
    }

    @Override
    public void onLoaderReset(Loader<Cursor> loader) {
        Logger.d(TAG, "onLoaderReset");
        invalidateOptionsMenu();
    }

    private void requestPermissions() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) return;

        List<String> permissions = new ArrayList<>();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions.add(Manifest.permission.POST_NOTIFICATIONS);
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permissions.add(Manifest.permission.BLUETOOTH_CONNECT);
        }
        if (!permissions.isEmpty()) {
            requestPermissions(permissions.toArray(new String[0]), PERMISSIONS_REQUEST_CODE);
        }
    }

    public View getPlayerTitleView() {
        return mManipulationPlayerTitle;
    }

}
