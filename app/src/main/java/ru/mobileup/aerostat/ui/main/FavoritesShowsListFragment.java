package ru.mobileup.aerostat.ui.main;

import androidx.loader.content.CursorLoader;

import ru.mobileup.aerostat.R;
import ru.mobileup.aerostat.storage.provider.Contract;
import ru.mobileup.aerostat.ui.main.ToolbarModeSwitcher;
import ru.mobileup.aerostat.ui.common.AbstractNotLoadableShowsListFragment;

/**
 * Created by terrakok on 16.01.15.
 */
public class FavoritesShowsListFragment extends AbstractNotLoadableShowsListFragment {

    @Override
    protected CursorLoader getCursorLoader() {
        String selection = Contract.ShowReleaseTable.SHOW_FAVORITE + " = 1";//в SQLite true = 1

        String filter = getFilterSelection();
        if (filter != null) {
            selection += " AND " + filter;
        }

        return new CursorLoader(
                getActivity(),
                Contract.ShowReleaseTable.CONTENT_URI,
                null,
                selection,
                null,
                getSortOrder()
        );
    }

    @Override
    protected String getEmptyMessage() {
        if (getFilterSelection() == null) {
            return getString(R.string.empty_msg_favorites);
        } else {
            return getString(R.string.empty_msg_search);
        }
    }

    @Override
    public String getTitle() {
        return getString(R.string.main_menu_action_favorite_title);
    }

    @Override
    public int getToolbarMode() {
        return ToolbarModeSwitcher.TOOLBAR_MODE_MAIN;
    }
}
