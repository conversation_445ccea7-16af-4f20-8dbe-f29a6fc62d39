package ru.mobileup.aerostat.ui.common.showcase

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.os.Build
import uk.co.deanwild.materialshowcaseview.shape.RectangleShape

class MaterialShowcaseRectangleRoundCornersShape(
    bounds: Rect,
    private val paddingLeft: Int = 0,
    private val paddingTop: Int = 0,
    private val paddingRight: Int = 0,
    private val paddingBottom: Int = 0,
    private val cornerRadius: Float = 0F
): RectangleShape(bounds) {

    override fun draw(canvas: Canvas, paint: Paint, x: Int, y: Int) {
        canvas.drawRoundRect(
            (-width / 2) + x - paddingLeft.toFloat(),
            (-height / 2) + y - paddingTop.toFloat(),
            (width / 2) + x + paddingRight.toFloat(),
            (height / 2) + y + paddingBottom.toFloat(),
            cornerRadius,
            cornerRadius,
            paint
        )
    }

}