package ru.mobileup.aerostat.ui.main.show

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import ru.mobileup.aerostat.R
import ru.mobileup.aerostat.api.model.CueTrackData

class PlaylistAdapter(
    private val onItemClicks: (CueTrackData) -> Unit
) : RecyclerView.Adapter<PlaylistAdapter.TrackViewHolder>() {

    var items: List<CueTrackData> = emptyList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TrackViewHolder {
        return TrackViewHolder(
            LayoutInflater.from(parent.context).inflate(R.layout.item_track, parent, false)
        )
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: TrackViewHolder, position: Int) {
        holder.bind(items[position])
    }

    inner class TrackViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        private val title: TextView = itemView.findViewById(R.id.title)
        private val subtitle: TextView = itemView.findViewById(R.id.subtitle)

        init {
            itemView.setOnClickListener { onItemClicks.invoke(items[adapterPosition]) }
        }

        fun bind(trackData: CueTrackData) {
            title.text = trackData.performer
            subtitle.text = trackData.title
        }
    }
}