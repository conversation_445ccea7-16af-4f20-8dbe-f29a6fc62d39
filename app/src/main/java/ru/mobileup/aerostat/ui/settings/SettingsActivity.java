package ru.mobileup.aerostat.ui.settings;

import android.os.Bundle;
import android.view.MenuItem;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import ru.mobileup.aerostat.R;
import ru.mobileup.aerostat.ui.main.ToolbarModeSwitcher;
import ru.mobileup.aerostat.util.InsetUtilsKt;

/**
 * Created by terrakok on 24.02.15.
 */
public class SettingsActivity extends AppCompatActivity implements ToolbarModeSwitcher {

    public static String EXTRA_FORCE_CHOOSE_DOWNLOAD_DIRECTORY = "EXTRA_FORCE_CHOOSE_DOWNLOAD_DIRECTORY";

    private Toolbar mToolbar;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        InsetUtilsKt.enableEdgeToEdge(this);
        setContentView(R.layout.activity_settings);
        mToolbar = findViewById(R.id.main_toolbar);

        setupToolbar();

        if (savedInstanceState == null) {
            SettingsFragment mSettingsFragment = new SettingsFragment();
            boolean forceChooseDownloadDirectory = getIntent().getBooleanExtra(EXTRA_FORCE_CHOOSE_DOWNLOAD_DIRECTORY, false);
            Bundle args = new Bundle();
            args.putBoolean(SettingsFragment.ARG_FORCE_CHOOSE_DOWNLOAD_DIRECTORY, forceChooseDownloadDirectory);
            mSettingsFragment.setArguments(args);

            getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.container, mSettingsFragment)
                    .commit();
        }
    }

    private void setupToolbar() {
        setSupportActionBar(mToolbar);
        switchToolbarMode(TOOLBAR_MODE_MAIN);

        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
    }

    @Override
    public void setToolbarTitle(String title) {
        getSupportActionBar().setTitle(title);
    }

    @Override
    public void setToolbarSubtitle(String subtitle) {

    }

    @Override
    public void switchToolbarMode(int mode) {

    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        switch (menuItem.getItemId()) {
            case android.R.id.home:
                onBackPressed();
                return true;
            default:
                return super.onOptionsItemSelected(menuItem);
        }
    }
}
