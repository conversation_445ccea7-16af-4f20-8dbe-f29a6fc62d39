package ru.mobileup.aerostat.ui.main;

import androidx.loader.content.CursorLoader;

import ru.mobileup.aerostat.R;
import ru.mobileup.aerostat.api.model.ShowRelease;
import ru.mobileup.aerostat.storage.provider.Contract;
import ru.mobileup.aerostat.ui.main.ToolbarModeSwitcher;
import ru.mobileup.aerostat.ui.common.AbstractNotLoadableShowsListFragment;

/**
 * Created by terrakok on 24.02.15.
 */
public class SavedShowsListFragment extends AbstractNotLoadableShowsListFragment {

    @Override
    protected CursorLoader getCursorLoader() {
        String selection = Contract.ShowReleaseTable.SHOW_SAVED_STATUS + " != " + ShowRelease.STATUS_NOT_SAVED;

        String filter = getFilterSelection();
        if (filter != null) {
            selection += " AND " + filter;
        }

        return new CursorLoader(
                getActivity(),
                Contract.ShowReleaseTable.CONTENT_URI,
                null,
                selection,
                null,
                getSortOrder()
        );
    }

    @Override
    protected String getEmptyMessage() {
        if (getFilterSelection() == null) {
            return getString(R.string.empty_msg_saved);
        } else {
            return getString(R.string.empty_msg_search);
        }
    }

    @Override
    public String getTitle() {
        return getString(R.string.saved_title);
    }

    @Override
    public int getToolbarMode() {
        return ToolbarModeSwitcher.TOOLBAR_MODE_MAIN;
    }
}
