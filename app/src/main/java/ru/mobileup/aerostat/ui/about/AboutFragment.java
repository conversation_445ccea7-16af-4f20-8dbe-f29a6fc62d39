package ru.mobileup.aerostat.ui.about;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;

import ru.mobileup.aerostat.BuildConfig;
import ru.mobileup.aerostat.R;
import ru.mobileup.aerostat.ui.common.BaseFragment;
import ru.mobileup.aerostat.ui.main.ToolbarModeSwitcher;

/**
 * Created by terrakok on 12.02.15.
 */
public class AboutFragment extends BaseFragment {

    private View mLogo, mName, mFace;
    private TextView mTextBox;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_about, container, false);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        ((TextView) view.findViewById(R.id.about_version)).setText(getString(R.string.version, BuildConfig.VERSION_NAME));

        mLogo = view.findViewById(R.id.about_logo);
        mName = view.findViewById(R.id.about_company_name);
        mFace = view.findViewById(R.id.about_face);
        mTextBox = (TextView) view.findViewById(R.id.about_textbox);

        mFace.setTranslationX(1000);
        mFace.setTranslationY(1000);
        mFace.setVisibility(View.GONE);
        mFace.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mFace.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mTextBox.setText(R.string.dev_mail);
                    }
                });
            }
        });

        mLogo.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                mName.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mName.setOnLongClickListener(new View.OnLongClickListener() {
                            @Override
                            public boolean onLongClick(View v) {
                                mName.setOnClickListener(null);
                                mName.setOnLongClickListener(null);
                                mFace.setVisibility(View.VISIBLE);
                                mFace.animate().translationX(0).translationY(0).setDuration(700);
                                mFace.postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        mFace.animate().translationX(1000).translationY(1000).setDuration(700);
                                        mFace.postDelayed(new Runnable() {
                                            @Override
                                            public void run() {
                                                mFace.setVisibility(View.GONE);
                                            }
                                        }, 700);
                                    }
                                }, 1000);
                                return true;
                            }
                        });
                    }
                });
                return true;
            }
        });
    }

    @Override
    public String getTitle() {
        return getString(R.string.about);
    }

    @Override
    public int getToolbarMode() {
        return ToolbarModeSwitcher.TOOLBAR_MODE_SIMPLE;
    }
}
