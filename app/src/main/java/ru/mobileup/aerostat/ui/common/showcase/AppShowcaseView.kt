package ru.mobileup.aerostat.ui.common.showcase

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.view.View
import android.view.ViewGroup
import eightbitlab.com.blurview.BlurAlgorithm
import eightbitlab.com.blurview.PublicBlurController
import eightbitlab.com.blurview.RenderScriptBlur
import ru.mobileup.aerostat.extension.fieldByName
import uk.co.deanwild.materialshowcaseview.IShowcaseListener
import uk.co.deanwild.materialshowcaseview.MaterialShowcaseView
import uk.co.deanwild.materialshowcaseview.shape.Shape

class AppShowcaseView constructor(
    context: Context,
    private val blurAlgorithmImpl: BlurAlgorithm = RenderScriptBlur(context)
) : MaterialShowcaseView(context),
    BlurAlgorithm by blurAlgorithmImpl {

    private val shape: Shape by fieldByName("mShape")
    private val xPosition: Int by fieldByName("mXPosition")
    private val yPosition: Int by fieldByName("mYPosition")
    private val contentTextView: View by fieldByName("mContentTextView")
    private var contentBottomMargin: Int by fieldByName("mContentBottomMargin")

    private val blurController: PublicBlurController

    init {
        val decorView: View = (context as Activity).window.decorView
        val rootView = decorView.findViewById<View>(android.R.id.content) as ViewGroup
        val windowBackground = decorView.background

        blurController = PublicBlurController(this, rootView, Color.TRANSPARENT).apply {
            setFrameClearDrawable(windowBackground)
            setBlurAlgorithm(this@AppShowcaseView)
            setBlurRadius(7F)
            setHasFixedTransformationMatrix(true)
        }
    }

    override fun blur(bitmap: Bitmap, blurRadius: Float): Bitmap {
        if (measuredWidth <= 0 || measuredHeight <= 0) return bitmap

        val blurBitmap = blurAlgorithmImpl.blur(bitmap, blurRadius)
        val blurScaledBitmap = Bitmap.createScaledBitmap(blurBitmap, measuredWidth, measuredHeight, false)

        val canvas = Canvas(blurScaledBitmap)

        val eraser = Paint().apply {
            color = -0x1
            xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
            flags = Paint.ANTI_ALIAS_FLAG
        }

        shape.draw(canvas, eraser, xPosition, yPosition)

        return Bitmap.createScaledBitmap(blurScaledBitmap, bitmap.width, bitmap.height, false)
    }

    override fun draw(canvas: Canvas) {
        val shouldDraw: Boolean = blurController.draw(canvas)

        if (shouldDraw) {
            super.draw(canvas)
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        blurController.updateBlurViewSize()
    }

    fun showWithConfig(
        config: AppShowcaseConfig,
        onDisplay: (() -> Unit)? = null,
        onDismiss: (() -> Unit)? = null
    ) {
        addShowcaseListener(object : IShowcaseListener {
            override fun onShowcaseDisplayed(showcaseView: MaterialShowcaseView?) {
                onDisplay?.invoke()
            }

            override fun onShowcaseDismissed(showcaseView: MaterialShowcaseView?) {
                onDismiss?.invoke()
            }
        })

        setConfig(config)
        config.gravity?.let { setGravity(it) }
        config.contentBottomMargin?.let { contentBottomMargin = it }
        config.contentTextAlpha?.let { contentTextView.alpha = it }

        show(context as Activity)
    }

    class Builder(activity: Activity) : MaterialShowcaseView.Builder(activity) {

        private var shadowShowcaseView: AppShowcaseView by fieldByName("showcaseView")

        init {
            shadowShowcaseView = AppShowcaseView(activity)
        }

        override fun singleUse(showcaseID: String?) = super.singleUse(showcaseID) as Builder

        override fun setDelay(delayInMillis: Int) = super.setDelay(delayInMillis) as Builder

        override fun setTarget(target: View?) = super.setTarget(target) as Builder

        override fun setTitleText(text: CharSequence?) = super.setTitleText(text) as Builder

        override fun setContentText(text: CharSequence?) = super.setContentText(text) as Builder

        override fun setDismissText(dismissText: CharSequence?) =
            super.setDismissText(dismissText) as Builder

        override fun setShapePadding(padding: Int) = super.setShapePadding(padding) as Builder

        override fun setShape(shape: Shape?) = super.setShape(shape) as Builder

        override fun build() = super.build() as AppShowcaseView
    }
}