package ru.mobileup.aerostat.ui.about;

import android.os.Bundle;
import android.view.MenuItem;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import ru.mobileup.aerostat.R;
import ru.mobileup.aerostat.ui.main.ToolbarModeSwitcher;
import ru.mobileup.aerostat.util.InsetUtilsKt;

/**
 * Created by terrakok on 24.02.15.
 */
public class AboutActivity extends AppCompatActivity implements ToolbarModeSwitcher {

    private Toolbar mToolbar;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        InsetUtilsKt.enableEdgeToEdge(this);
        setContentView(R.layout.activity_about);
        mToolbar = (Toolbar) findViewById(R.id.main_toolbar);

        setupToolbar();

        if (savedInstanceState == null) {
            getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.container, new AboutFragment())
                    .commit();
        }
    }

    private void setupToolbar() {
        setSupportActionBar(mToolbar);
        switchToolbarMode(TOOLBAR_MODE_MAIN);

        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeButtonEnabled(true);
    }

    @Override
    public void setToolbarTitle(String title) {
        getSupportActionBar().setTitle(title);
    }

    @Override
    public void setToolbarSubtitle(String subtitle) {

    }

    @Override
    public void switchToolbarMode(int mode) {

    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        switch (menuItem.getItemId()) {
            case android.R.id.home:
                onBackPressed();
                return true;
            default:
                return super.onOptionsItemSelected(menuItem);
        }
    }
}
