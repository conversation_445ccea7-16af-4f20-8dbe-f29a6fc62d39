package ru.mobileup.aerostat.ui.main.list;

import android.database.Cursor;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.loader.app.LoaderManager;
import androidx.loader.content.CursorLoader;
import androidx.loader.content.Loader;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import java.util.Date;

import dmdevgo.hunky.core.BaseProcessor;
import dmdevgo.hunky.core.HunkyManager;
import dmdevgo.hunky.core.Report;
import dmdevgo.hunky.core.ServiceCallbackListener;
import ru.mobileup.aerostat.R;
import ru.mobileup.aerostat.api.processor.GetNewShowReleasesProcessor;
import ru.mobileup.aerostat.storage.AerostatPrefs;
import ru.mobileup.aerostat.storage.provider.Contract;
import ru.mobileup.aerostat.ui.common.BaseFragment;
import ru.mobileup.aerostat.ui.common.FilterableFragment;
import ru.mobileup.aerostat.ui.main.ToolbarModeSwitcher;
import ru.mobileup.aerostat.util.Logger;

/**
 * Created by terrakok on 16.01.15.
 */
public class ShowsListFragment extends BaseFragment implements LoaderManager.LoaderCallbacks<Cursor>, FilterableFragment {
    private static final String TAG = "ShowsListFragment";
    private static final String STATE_NEW_SHOW_REQUEST_ID = "slf_new_show_req";
    private static final String STATE_LAST_NEW_SHOW_REQUEST_TIME = "slf_last_req_time";

    // Интервал между автоматическими запросами новых выпусков
    private static final long AUTO_REQUEST_INTERVAL_MS = 30 * 60 * 1000; // 30 минут в миллисекундах

    private String mNewShowRequestTag = "";

    private Date lastNewShowRequestTime = null;

    private SwipeRefreshLayout mSwipeRefreshLayout;
    private RecyclerView mRecyclerView;
    private ShowReleaseRecyclerAdapter mRecyclerAdapter;
    private TextView mEmptyView;
    private TextView mNetworkErrorText;
    private View mNetworkErrorIcon;

    private String mCurFilter;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_shows_list, container, false);
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mRecyclerView = (RecyclerView) view.findViewById(R.id.fr_recycler_view);
        mSwipeRefreshLayout = (SwipeRefreshLayout) view.findViewById(R.id.fr_list_swipe_refresh);
        mEmptyView = (TextView) view.findViewById(R.id.empty_list_view);
        mNetworkErrorText = view.findViewById(R.id.network_error_text);
        mNetworkErrorIcon = view.findViewById(R.id.network_error_icon);
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        initializeSwipeRefreshAndRecyclerView();

        // Restore states
        if (savedInstanceState != null) {
            mNewShowRequestTag = savedInstanceState.getString(STATE_NEW_SHOW_REQUEST_ID);
            long lastRequestTimeMs = savedInstanceState.getLong(STATE_LAST_NEW_SHOW_REQUEST_TIME, 0);
            if (lastRequestTimeMs > 0) {
                lastNewShowRequestTime = new Date(lastRequestTimeMs);
            }
        }

        // And start loader
        getLoaderManager().initLoader(0, null, this);
        mSwipeRefreshLayout.setRefreshing(true); // load new shows
    }

    private void initializeSwipeRefreshAndRecyclerView() {
        mSwipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                doNewShowListRequest(AerostatPrefs.getMaxShowNumber(getActivity(), 1));
            }
        });
        mSwipeRefreshLayout.setColorSchemeResources(R.color.accent, R.color.primary);

        mRecyclerView.setHasFixedSize(true);

        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));

        mRecyclerAdapter = new ShowReleaseRecyclerAdapter(null, getActivity());
        mRecyclerView.setAdapter(mRecyclerAdapter);
        mRecyclerView.addItemDecoration(new DividerItemDecoration(getContext(), LinearLayoutManager.VERTICAL));
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString(STATE_NEW_SHOW_REQUEST_ID, mNewShowRequestTag);
        if (lastNewShowRequestTime != null) {
            outState.putLong(STATE_LAST_NEW_SHOW_REQUEST_TIME, lastNewShowRequestTime.getTime());
        }
    }

    private void setSwipeRefreshProgressState(final boolean showProgress) {
        mSwipeRefreshLayout.post(new Runnable() {
            @Override
            public void run() {
                mSwipeRefreshLayout.setRefreshing(showProgress);
            }
        });
    }

    @Override
    public void onStart() {
        super.onStart();
        HunkyManager.getInstance().registerServiceCallbackListener(mServiceCallbackListener);

        //resume request state
        setSwipeRefreshProgressState(HunkyManager.getInstance().isRequestRunning(mNewShowRequestTag));

        autoRequestNewShows();
    }

    @Override
    public void onStop() {
        HunkyManager.getInstance().unregisterServiceCallbackListener(mServiceCallbackListener);

        //скидываем фильтр, когда уходим в фон, так как SearchWidget тоже скидывается
        if (mCurFilter != null) {
            filterData(null);
        }
        super.onStop();
    }

    private void doNewShowListRequest(int lastShowNumber) {
        if (!HunkyManager.getInstance().isRequestRunning(mNewShowRequestTag)) {
            GetNewShowReleasesProcessor processor = new GetNewShowReleasesProcessor(lastShowNumber);
            mNewShowRequestTag = processor.getTag();
            HunkyManager.getInstance().startRequest(processor);
            lastNewShowRequestTime = new Date();
        }
    }

    private void autoRequestNewShows() {
        if (lastNewShowRequestTime == null
                || System.currentTimeMillis() - lastNewShowRequestTime.getTime() >= AUTO_REQUEST_INTERVAL_MS) {

            // Проверяем, нужна ли принудительная загрузка всех выпусков (раз в неделю)
            boolean shouldForceReload = AerostatPrefs.shouldForceReloadAllShows(getActivity());
            int startShowNumber = shouldForceReload ? 1 : AerostatPrefs.getMaxShowNumber(getActivity(), 1);

            doNewShowListRequest(startShowNumber);
        }
    }

    private ServiceCallbackListener mServiceCallbackListener = new ServiceCallbackListener() {
        @Override
        public boolean onServiceCallback(BaseProcessor processor, Report result) {
            if (processor.getTag().equals(mNewShowRequestTag)) {
                if (result.isFinished() || result.isCanceled()) {
                    setSwipeRefreshProgressState(false);

                    if (result.getStatus() == Report.Status.FAILURE) {
                        Logger.e(TAG, result.getError().getMessage());

                        if (mRecyclerAdapter.getItemCount() == 0) {
                            mNetworkErrorText.setVisibility(View.VISIBLE);
                            mNetworkErrorIcon.setVisibility(View.VISIBLE);
                        } else {
                            Toast.makeText(getActivity(), getString(R.string.smth_error), Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        mNetworkErrorText.setVisibility(View.GONE);
                        mNetworkErrorIcon.setVisibility(View.GONE);
                    }

                } else if (result.isRunning()) {
                    setSwipeRefreshProgressState(true);
                }
                return true;
            }
            return false;
        }
    };

    private CursorLoader getShowReleaseCursorLoader() {
        // All releases params
        String selection = null;
        String[] projection = null;
        String orderBy = Contract.ShowReleaseTable.SHOW_NUMBER + (AerostatPrefs.getSortPlaylist(getContext()) ? " ASC" : " DESC");

        // Change params if they are for search
        if (mCurFilter != null) {
            String selectionInNumbers = null;
            String selectionInNames = Contract.ShowReleaseTable.SHOW_NAME_SEARCH + " LIKE '%" + mCurFilter.toUpperCase() + "%'";
            String selectionInDescriptions = Contract.ShowReleaseTable.SHOW_DESCRIPTION_SEARCH + " LIKE '%" + mCurFilter.toUpperCase() + "%'";

            if (TextUtils.isDigitsOnly(mCurFilter)) {
                selectionInNumbers = Contract.ShowReleaseTable.SHOW_NUMBER + "=" + Integer.valueOf(mCurFilter);
                selection = selectionInNumbers + " OR " + selectionInNames + " OR " + selectionInDescriptions;
                orderBy = selectionInNumbers + " DESC, " + orderBy;
            } else {
                selection = selectionInNames + " OR " + selectionInDescriptions;
                orderBy = selectionInNames + " DESC, " + orderBy;
            }

            projection = new String[]{
                    // All columns
                    Contract.ShowReleaseTable._ID,
                    Contract.ShowReleaseTable.SHOW_NUMBER,
                    Contract.ShowReleaseTable.SHOW_NAME,
                    Contract.ShowReleaseTable.SHOW_SUBTITLE,
                    Contract.ShowReleaseTable.SHOW_DESCRIPTION,
                    Contract.ShowReleaseTable.SHOW_FILE_URL,
                    Contract.ShowReleaseTable.SHOW_CUE_URL,
                    Contract.ShowReleaseTable.SHOW_FAVORITE,
                    Contract.ShowReleaseTable.SHOW_SAVED_STATUS,
                    Contract.ShowReleaseTable.SHOW_DOWNLOADING_INDEX,
                    Contract.ShowReleaseTable.SHOW_SAVED_FILE_PATH,
                    Contract.ShowReleaseTable.SHOW_LISTENING_PROGRESS,
                    // plus the row with count of rows where filter found in the name_search column
                    Contract.ShowReleaseTable.sqlFoundInNamesCount(mCurFilter.toUpperCase())
                            + " AS " + Contract.ShowReleaseTable.FOUND_IN_NAMES_COUNT
            };

            orderBy += ", " + Contract.ShowReleaseTable._ID + " ASC";
        }

        // Create cursor
        return new CursorLoader(
                getActivity(),
                Contract.ShowReleaseTable.CONTENT_URI,
                projection,
                selection,
                null,
                orderBy
        );
    }

    @Override
    public Loader<Cursor> onCreateLoader(int id, Bundle args) {
        Logger.d(TAG, "onCreateLoader id = " + id);
        return getShowReleaseCursorLoader();
    }

    @Override
    public void onLoadFinished(Loader<Cursor> loader, Cursor data) {
        Logger.d(TAG, "onLoadFinished data.getCount() = " + data.getCount());

        if (data.getCount() > 0) {
            hideEmptyView();
        } else {
            showEmptyView(getEmptyMessage());
        }

        mRecyclerAdapter.swapCursor(data);

        //если база пуста и это не из-за фильтра (первый запуск или дропнули для новой версии)
        if (data.getCount() == 0 && mCurFilter == null) {
            doNewShowListRequest(1);
        }
    }

    @Override
    public void onLoaderReset(Loader<Cursor> loader) {
        Logger.d(TAG, "onLoaderReset");
        mRecyclerAdapter.swapCursor(null);
    }

    private void hideEmptyView() {
        mEmptyView.setVisibility(View.GONE);
    }

    private void showEmptyView(String message) {
        mEmptyView.setText(message);
        mEmptyView.setVisibility(View.VISIBLE);
    }

    private String getEmptyMessage() {
        if (mCurFilter == null) {
            return getString(R.string.empty_msg_main);
        } else {
            return getString(R.string.empty_msg_search);
        }
    }

    @Override
    public String getTitle() {
        return getString(R.string.all_shows_title);
    }

    @Override
    public int getToolbarMode() {
        return ToolbarModeSwitcher.TOOLBAR_MODE_MAIN;
    }

    @Override
    public void filterData(String newText) {
        mCurFilter = !TextUtils.isEmpty(newText) ? newText : null;
        getLoaderManager().restartLoader(0, null, this);
    }

    @Override
    public String getFilter() {
        return mCurFilter;
    }
}
