package ru.mobileup.aerostat.ui.main.show

import android.content.ActivityNotFoundException
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import ru.mobileup.aerostat.R
import ru.mobileup.aerostat.api.model.ShowRelease
import ru.mobileup.aerostat.cue.CueHelper
import ru.mobileup.aerostat.integration.ThirdpartyMusicService
import ru.mobileup.aerostat.integration.yandex.YandexMusic
import ru.mobileup.aerostat.ui.common.BaseFragment
import ru.mobileup.aerostat.ui.common.CurrentShowChangesListener
import ru.mobileup.aerostat.ui.common.bundleProperty
import ru.mobileup.aerostat.ui.main.ToolbarModeSwitcher

class ShowPlayListFragment
    : BaseFragment(),
    CurrentShowChangesListener {

    companion object {
        @JvmStatic
        fun getNewInstance(showRelease: ShowRelease, isCurrentPlayingShow: Boolean) =
            ShowPlayListFragment().apply {
                this.showRelease = showRelease
                this.isPlayingCurrentShow = isCurrentPlayingShow
            }
    }

    private lateinit var playlistRecyclerView: RecyclerView

    private val yandexMusic: ThirdpartyMusicService by lazy { YandexMusic() }

    var showRelease: ShowRelease by bundleProperty()
    private var isPlayingCurrentShow: Boolean by bundleProperty()

    private val playlistAdapter = PlaylistAdapter {
        GlobalScope.launch(Dispatchers.Main) {
            val progressDialog = AlertDialog.Builder(requireContext())
                .setCancelable(false)
                .setView(R.layout.dialog_progress)
                .create()

            try {
                progressDialog.show()
                val trackIntent = withContext(Dispatchers.IO) {
                    yandexMusic.trackIntent(it.performer, it.title)
                }
                progressDialog.dismiss()
                startActivity(trackIntent)
            } catch (e: CancellationException) {
                throw e
            } catch (e: Exception) {
                try {
                    progressDialog.dismiss()
                    startActivity(yandexMusic.searchIntent(it.performer, it.title))
                } catch (e: ActivityNotFoundException) {
                    Toast.makeText(
                        requireContext(),
                        R.string.missing_external_app_error,
                        Toast.LENGTH_LONG
                    ).show()
                }
            }
        }
    }

    override fun getTitle() = showRelease.number.toString() + " — " + showRelease.name

    override fun getSubtitle() = showRelease.subTitle.orEmpty()

    override fun getToolbarMode(): Int {
        return ToolbarModeSwitcher.TOOLBAR_MODE_PLAYLIST
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_play_list_yandex_music, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        playlistRecyclerView = view.findViewById(R.id.playlistRecyclerView)
        playlistRecyclerView.layoutManager = LinearLayoutManager(context)
        playlistRecyclerView.addItemDecoration(
            DividerItemDecoration(
                context,
                LinearLayoutManager.VERTICAL
            )
        )
        playlistRecyclerView.adapter = playlistAdapter

        invalidateToolbarMode()

        GlobalScope.launch(Dispatchers.Main) {
            if (playlistAdapter.items.isEmpty()) {
                try {
                    playlistAdapter.items = withContext(Dispatchers.Default) {
                        CueHelper.getCueData(showRelease)
                    }
                        .filter { it.performer != getString(R.string.prefix_track_name_bg) }
                        .sortedBy { it.startTime }
                    playlistRecyclerView.adapter = playlistAdapter
                } catch (e: CancellationException) {
                    throw e
                } catch (e: Exception) {
                    AlertDialog.Builder(requireContext())
                        .setMessage(getString(R.string.dialog_error_no_internet_connection_msg))
                        .setPositiveButton(R.string.common_close) { dialog, _ -> dialog.dismiss() }
                        .create()
                        .show()
                }
            }
        }
    }

    override fun onCurrentShowChange(currentShowRelease: ShowRelease?) {
        isPlayingCurrentShow = showRelease.number == currentShowRelease?.number
    }
}