package ru.mobileup.aerostat.ui.settings

import android.app.Activity.RESULT_OK
import android.content.Intent
import android.content.Intent.FLAG_GRANT_WRITE_URI_PERMISSION
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.DocumentsContract
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.core.view.isVisible
import ru.mobileup.aerostat.R
import ru.mobileup.aerostat.storage.DownloadManager
import ru.mobileup.aerostat.ui.common.BaseFragment
import ru.mobileup.aerostat.ui.main.ToolbarModeSwitcher
import ru.mobileup.aerostat.util.FileUtils
import ru.mobileup.aerostat.util.ShowReleaseUtils


class SettingsFragment : BaseFragment() {

    companion object {
        private const val REQUEST_CHOOSE_DIRECTORY = 200
        const val ARG_FORCE_CHOOSE_DOWNLOAD_DIRECTORY = "ARG_FORCE_CHOOSE_DOWNLOAD_DIRECTORY"
    }

    private lateinit var forceChooseDownloadPathWarning: TextView
    private lateinit var downloadPathText: TextView

    override fun getTitle(): String = getString(R.string.settings)

    override fun getToolbarMode() = ToolbarModeSwitcher.TOOLBAR_MODE_SIMPLE

    private val forceChooseDownloadDirectory by lazy {
        arguments?.getBoolean(ARG_FORCE_CHOOSE_DOWNLOAD_DIRECTORY) ?: false
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return inflater.inflate(R.layout.fragment_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        forceChooseDownloadPathWarning = view.findViewById(R.id.force_choose_download_path_warning)
        downloadPathText = view.findViewById(R.id.downloadPathText)

    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        val downloadDirectory = DownloadManager.getDownloadDirectory()

        showDownloadPath(downloadDirectory)

        forceChooseDownloadPathWarning.isVisible = forceChooseDownloadDirectory

        view?.setOnClickListener {
            val dir = FileUtils.uriToDocumentFile(downloadDirectory, context)

            if (dir != null && dir.exists()) {
                showDirectoryChooser(downloadDirectory)
            } else {
                showDirectoryChooser()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == REQUEST_CHOOSE_DIRECTORY
            && resultCode == RESULT_OK
            && data?.data != null
        ) {
            onDirectoryChosen(data.data!!)
        }
    }

    private fun showDirectoryChooser(uri: Uri? = null) {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT_TREE).apply {
            flags = Intent.FLAG_GRANT_READ_URI_PERMISSION or FLAG_GRANT_WRITE_URI_PERMISSION
            putExtra("android.content.extra.SHOW_ADVANCED", true)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                putExtra(DocumentsContract.EXTRA_INITIAL_URI, uri)
            }
        }
        startActivityForResult(intent, REQUEST_CHOOSE_DIRECTORY)
    }

    private fun onDirectoryChosen(uri: Uri) {
        if (FileUtils.canWrite(uri, requireContext())) {
            val flags = Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
            requireContext().contentResolver.takePersistableUriPermission(uri, flags)
            DownloadManager.setDownloadDirectory(uri)
            ShowReleaseUtils.checkAllShowsDownloadStatus(requireContext().applicationContext)
            showDownloadPath(uri)
            if (forceChooseDownloadDirectory) {
                requireActivity().setResult(RESULT_OK)
                requireActivity().finish()
            }
        } else {
            Toast.makeText(
                activity,
                getString(R.string.settings_download_path_write_error),
                Toast.LENGTH_SHORT
            ).show()

            showDirectoryChooser(uri)
        }
    }

    private fun showDownloadPath(uri: Uri) {
        if (DownloadManager.isDownloadDirectoryAvailable()) {
            downloadPathText.text = uri.path
        } else {
            downloadPathText.text = getString(R.string.settings_download_path_unselected)
        }
    }
}
