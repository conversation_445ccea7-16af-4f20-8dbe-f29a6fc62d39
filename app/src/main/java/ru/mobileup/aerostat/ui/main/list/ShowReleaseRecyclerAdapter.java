package ru.mobileup.aerostat.ui.main.list;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.database.Cursor;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import java.lang.ref.WeakReference;

import ru.mobileup.aerostat.R;
import ru.mobileup.aerostat.api.model.ShowRelease;
import ru.mobileup.aerostat.storage.provider.Contract;
import ru.mobileup.aerostat.ui.common.CursorRecyclerAdapter;
import ru.mobileup.aerostat.ui.main.MainActivity;
import ru.mobileup.aerostat.util.ShowReleaseUtils;

/**
 * Created by terrakok on 05.02.15.
 */
public class ShowReleaseRecyclerAdapter extends CursorRecyclerAdapter<RecyclerView.ViewHolder> {
    private static final String TAG = "ShowReleaseRecyclerAdapter";

    private WeakReference<Activity> mActivityRef;

    private boolean mIsFooterShowing;

    // Search results counts from name and description columns
    private int mInNameResultsCount;
    private int mInDescriptionResultsCount;

    public ShowReleaseRecyclerAdapter(Cursor cursor, Activity activity) {
        super(cursor);
        mActivityRef = new WeakReference(activity);
        calculateCountsOfSearchResults(cursor);
    }

    @Override
    public Cursor swapCursor(Cursor newCursor) {
        calculateCountsOfSearchResults(newCursor);
        return super.swapCursor(newCursor);
    }

    /**
     * Calculates counts of search results if cursor has search-specific column.
     */
    private void calculateCountsOfSearchResults(Cursor cursor) {
        if (cursor != null) {
            int foundInNamesCountColumn =
                    cursor.getColumnIndex(Contract.ShowReleaseTable.FOUND_IN_NAMES_COUNT);

            if (foundInNamesCountColumn != -1) {
                // This is the search, need to get the results counts

                if (cursor.moveToFirst()) {
                    mInNameResultsCount = cursor.getInt(foundInNamesCountColumn);
                }

                mInDescriptionResultsCount = cursor.getCount() - mInNameResultsCount;
            } else {
                mInNameResultsCount = 0;
                mInDescriptionResultsCount = 0;
            }
        }
    }

    @Override
    public void onBindViewHolderCursor(RecyclerView.ViewHolder viewHolder, Cursor cursor) {
        ShowRelease showRelease = Contract.ShowReleaseTable.getShowInfo(cursor);
        ((ViewHolder) viewHolder).bind(showRelease);
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (getItemViewType(position) == R.id.adapter_view_type_item) {
            // Call only for items

            // Get real item position
            boolean isAfterDescriptionsLabel = mInDescriptionResultsCount > 0 && position > getDescriptionsLabelPosition();
            int realPosition = position
                    - (mInNameResultsCount > 0 ? 1 : 0)
                    - (isAfterDescriptionsLabel ? 1 : 0);

            // Pass it to the super
            super.onBindViewHolder(holder, realPosition);
        }
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parentView, int viewType) {
        switch (viewType) {
            case R.id.adapter_view_type_footer:
                View footerView = LayoutInflater.from(parentView.getContext()).inflate(R.layout.item_progress, parentView, false);
                return new ProgressFooterViewHolder(footerView);

            case R.id.adapter_view_type_search_label_from_name:
                TextView nameLabel = (TextView) LayoutInflater.from(parentView.getContext()).inflate(R.layout.item_search_label, parentView, false);
                nameLabel.setText(parentView.getContext().getString(R.string.search_label_from_name));
                return new LabelViewHolder(nameLabel);

            case R.id.adapter_view_type_search_label_from_description:
                TextView descriptionLabel = (TextView) LayoutInflater.from(parentView.getContext()).inflate(R.layout.item_search_label, parentView, false);
                descriptionLabel.setText(parentView.getContext().getString(R.string.search_label_from_description));
                return new LabelViewHolder(descriptionLabel);

            default:
                View itemView = LayoutInflater.from(parentView.getContext()).inflate(R.layout.item_show_release, parentView, false);
                return new ViewHolder(itemView);
        }
    }

    @Override
    public int getItemViewType(int position) {

        // Hypothetical positions
        int namesLabelPosition = 0;
        int descriptionsLabelPosition = getDescriptionsLabelPosition();
        int footerPosition = getItemCount() - 1;

        if (mIsFooterShowing && position == footerPosition) {
            // Footer
            return R.id.adapter_view_type_footer;

        } else {
            if (mInNameResultsCount > 0 && position == namesLabelPosition) {
                // Name label
                return R.id.adapter_view_type_search_label_from_name;

            } else if (mInDescriptionResultsCount > 0 && position == descriptionsLabelPosition) {
                // Description label
                return R.id.adapter_view_type_search_label_from_description;

            } else {
                // Items
                return R.id.adapter_view_type_item;
            }
        }
    }

    private int getDescriptionsLabelPosition() {
        return mInNameResultsCount > 0 ? mInNameResultsCount + 1 : 0;
    }

    @Override
    public int getItemCount() {
        return super.getItemCount()
                + ((mInNameResultsCount > 0) ? 1 : 0) // Should show names label
                + ((mInDescriptionResultsCount > 0) ? 1 : 0) // Should show descriptions label
                + (mIsFooterShowing ? 1 : 0);
    }

    public ShowRelease getItemByPosition(int position) {
        if (getItemViewType(position) == R.id.adapter_view_type_item) {
            return Contract.ShowReleaseTable.getShowInfo((Cursor) getItem(position));
        } else {
            return null;
        }
    }

    public void showProgressFooter() {
        if (!mIsFooterShowing) {
            int positionToInsert = getItemCount();
            mIsFooterShowing = true;
            notifyItemInserted(positionToInsert);
        }
    }

    public void hideProgressFooter() {
        if (mIsFooterShowing) {
            int positionToRemove = getItemCount();
            mIsFooterShowing = false;
            notifyItemRemoved(positionToRemove);
        }
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private View rootView;
        private ProgressBar progressBar;
        private TextView title;
        private TextView subTitle;
        private TextView number;
        private ImageView favorite;
        private ImageView statusDownloading;

        public ViewHolder(View view) {
            super(view);
            rootView = view;
            progressBar = view.findViewById(R.id.item_show_progress);
            title = view.findViewById(R.id.item_show_title);
            subTitle = view.findViewById(R.id.item_show_subtitle);
            number = view.findViewById(R.id.item_show_number);
            favorite = view.findViewById(R.id.item_show_fav);
            statusDownloading = view.findViewById(R.id.item_show_subtitle_status);
        }

        public void bind(final ShowRelease showRelease) {
            final Context context = rootView.getContext();

            number.setText(showRelease.getNumber() + "");
            title.setText(showRelease.getName());
            subTitle.setText(showRelease.getSubTitle());
            favorite.setImageResource(showRelease.isFavorite() ? R.drawable.ic_star : R.drawable.ic_star_outline);

            final int downloadStatus = showRelease.getSavedStatus();
            statusDownloading.clearAnimation();
            switch (downloadStatus) {
                case ShowRelease.STATUS_NOT_SAVED:
                    statusDownloading.setVisibility(View.GONE);
                    break;
                case ShowRelease.STATUS_DOWNLOADING:
                    statusDownloading.setVisibility(View.VISIBLE);
                    Animation animation = AnimationUtils.loadAnimation(context, R.anim.tv_animated_downloading);
                    statusDownloading.setImageResource(R.drawable.tv_downloading);
                    statusDownloading.startAnimation(animation);
                    break;
                case ShowRelease.STATUS_SAVED:
                    statusDownloading.setVisibility(View.VISIBLE);
                    statusDownloading.setImageResource(R.drawable.tv_file_download);
                    break;
            }

            favorite.setOnClickListener(v -> {
                ShowReleaseUtils.setFavorite(showRelease, !showRelease.isFavorite(), context);
            });

            progressBar.setProgress((int) (showRelease.getListeningProgress() * 100));

            progressBar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    MainActivity.playShowBroadcastMessage(context, showRelease);
                }
            });

            rootView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    MainActivity.openShowInfoBroadcastMessage(context, showRelease);
                }
            });

            rootView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(final View view) {

                    String listening_action;
                    String saving_action;

                    if (showRelease.isListened()) {
                        listening_action = view.getResources().getString(R.string.action_unmark_as_listened_title);
                    } else {
                        listening_action = view.getResources().getString(R.string.action_mark_as_listened_title);
                    }

                    switch (downloadStatus) {
                        case ShowRelease.STATUS_NOT_SAVED:
                            saving_action = view.getResources().getString(R.string.action_download_title);
                            break;
                        case ShowRelease.STATUS_DOWNLOADING:
                            saving_action = view.getResources().getString(R.string.action_cancel_loading_title);
                            break;
                        case ShowRelease.STATUS_SAVED:
                            saving_action = view.getResources().getString(R.string.action_delete_title);
                            break;
                        default:
                            throw new IllegalArgumentException();
                    }

                    new AlertDialog.Builder(view.getContext())
                            .setItems(
                                    new String[]{listening_action, saving_action},
                                    new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            switch (which) {
                                                case 0:
                                                    if (showRelease.isListened()) {
                                                        ShowReleaseUtils.setListeningTime(
                                                                showRelease,
                                                                0
                                                        );
                                                    }
                                                    ShowReleaseUtils.setListeningProgress(
                                                            showRelease,
                                                            showRelease.isListened() ? 0 : 1,
                                                            view.getContext()
                                                    );
                                                    break;
                                                case 1:
                                                    switch (downloadStatus) {
                                                        case ShowRelease.STATUS_NOT_SAVED:
                                                            ShowReleaseUtils.downloadShowOnDisk(showRelease, mActivityRef.get());
                                                            break;
                                                        case ShowRelease.STATUS_DOWNLOADING:
                                                            ShowReleaseUtils.cancelLoadingDialog(showRelease, view.getContext());
                                                            break;
                                                        case ShowRelease.STATUS_SAVED:
                                                            ShowReleaseUtils.showRemoveDialog(showRelease, view.getContext());
                                                            break;
                                                        default:
                                                            throw new IllegalArgumentException();
                                                    }
                                                    break;
                                            }
                                        }
                                    }

                            )
                            .setPositiveButton(R.string.common_cancel, null)
                            .create()
                            .show();
                    return true;
                }
            });
        }
    }

    public static class LabelViewHolder extends RecyclerView.ViewHolder {

        private static Typeface sRobotoMedium = null;

        public LabelViewHolder(TextView view) {
            super(view);

            if (sRobotoMedium == null) {
                sRobotoMedium = Typeface.createFromAsset(view.getContext().getAssets(), "Roboto-Medium.ttf");
            }
            view.setTypeface(sRobotoMedium);
        }
    }

    private static class ProgressFooterViewHolder extends RecyclerView.ViewHolder {
        public ProgressFooterViewHolder(View itemView) {
            super(itemView);
        }
    }
}
