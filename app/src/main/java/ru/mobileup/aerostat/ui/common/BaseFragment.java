package ru.mobileup.aerostat.ui.common;

import android.app.Activity;
import android.os.Bundle;

import androidx.fragment.app.Fragment;

import ru.mobileup.aerostat.ui.main.ToolbarModeSwitcher;

/**
 * Created by terrakok on 16.01.15.
 */
public abstract class BaseFragment extends Fragment implements OnBackPressedListener {

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setRetainInstance(true);
    }

    @Override
    public void onResume() {
        super.onResume();
        invalidateToolbarMode();
    }

    abstract public String getTitle();

    public String getSubtitle() {
        return null;
    }

    abstract public int getToolbarMode();

    protected void invalidateToolbarMode() {
        Activity activity = getActivity();
        if (activity instanceof ToolbarModeSwitcher) {
            ((ToolbarModeSwitcher) activity).setToolbarTitle(getTitle());
            ((ToolbarModeSwitcher) activity).setToolbarSubtitle(getSubtitle());
            ((ToolbarModeSwitcher) activity).switchToolbarMode(getToolbarMode());
        }
    }

    @Override
    public boolean onBackPressed() {
        return false;
    }
}
