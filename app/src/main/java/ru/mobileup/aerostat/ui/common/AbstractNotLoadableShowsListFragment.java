package ru.mobileup.aerostat.ui.common;

import android.database.Cursor;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.loader.app.LoaderManager;
import androidx.loader.content.CursorLoader;
import androidx.loader.content.Loader;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import ru.mobileup.aerostat.R;
import ru.mobileup.aerostat.api.model.ShowRelease;
import ru.mobileup.aerostat.storage.AerostatPrefs;
import ru.mobileup.aerostat.storage.provider.Contract;
import ru.mobileup.aerostat.ui.main.list.ShowReleaseRecyclerAdapter;
import ru.mobileup.aerostat.util.Logger;

/**
 * Created by terrakok on 24.02.15.
 */
public abstract class AbstractNotLoadableShowsListFragment extends BaseFragment implements LoaderManager.LoaderCallbacks<Cursor>, FilterableFragment {
    private static final String TAG = "AbstractNotLoadableShowListFragment";

    private SwipeRefreshLayout mSwipeRefreshLayout;
    private RecyclerView mRecyclerView;
    private ShowReleaseRecyclerAdapter mRecyclerAdapter;
    private TextView mEmptyView;

    private String mCurFilter;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_shows_list, container, false);
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mRecyclerView = (RecyclerView) view.findViewById(R.id.fr_recycler_view);
        mSwipeRefreshLayout = (SwipeRefreshLayout) view.findViewById(R.id.fr_list_swipe_refresh);
        mEmptyView = (TextView) view.findViewById(R.id.empty_list_view);
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        initializeSwipeRefreshAndRecyclerView();

        // And start loader
        getLoaderManager().initLoader(0, null, this);
    }

    @Override
    public void onStop() {
        //скидываем фильтр, когда уходим в фон, так как SearchWidget тоже скидывается
        if (mCurFilter != null) {
            filterData(null);
        }
        super.onStop();
    }

    protected String getFilterSelection() {
        String result = null;

        if (mCurFilter != null) {
            result = Contract.ShowReleaseTable.SHOW_NAME_SEARCH + " LIKE '%" + mCurFilter.toUpperCase() + "%'";

            if (TextUtils.isDigitsOnly(mCurFilter)) {
                result = "(" + Contract.ShowReleaseTable.SHOW_NUMBER + "=" + Integer.valueOf(mCurFilter) + " OR " + result + ")";
            }
        }

        return result;
    }

    protected String getSortOrder() {
        String result = Contract.ShowReleaseTable.SHOW_NUMBER
                + (AerostatPrefs.getSortPlaylist(getContext()) ? " ASC" : " DESC");

        if (mCurFilter != null && TextUtils.isDigitsOnly(mCurFilter)) {
            result = Contract.ShowReleaseTable.SHOW_NUMBER + "=" + Integer.valueOf(mCurFilter) + " DESC, " + result;
        }

        return result;
    }

    private void initializeSwipeRefreshAndRecyclerView() {
        mSwipeRefreshLayout.setEnabled(false);
        mRecyclerView.setHasFixedSize(true);

        mRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));

        mRecyclerAdapter = new ShowReleaseRecyclerAdapter(null, getActivity());
        mRecyclerView.setAdapter(mRecyclerAdapter);
        mRecyclerView.addItemDecoration(new DividerItemDecoration(getContext(), LinearLayoutManager.VERTICAL));
    }

    protected abstract CursorLoader getCursorLoader();

    @Override
    public Loader<Cursor> onCreateLoader(int id, Bundle args) {
        Logger.d(TAG, "onCreateLoader id = " + id);
        return getCursorLoader();
    }

    @Override
    public void onLoadFinished(Loader<Cursor> loader, Cursor data) {
        Logger.d(TAG, "onLoadFinished data.getCount() = " + data.getCount());
        if (data.getCount() > 0) {
            hideEmptyView();
        } else {
            showEmptyView(getEmptyMessage());
        }

        swapData(mRecyclerAdapter.getCursor(), data);
    }

    @Override
    public void onLoaderReset(Loader<Cursor> loader) {
        Logger.d(TAG, "onLoaderReset");
        mRecyclerAdapter.swapCursor(null);
    }

    private void swapData(Cursor oldCursor, Cursor newCursor) {
        //если исчез один элемент, надо проанимировать
        if (oldCursor != null && newCursor != null && oldCursor.getCount() == newCursor.getCount() + 1) {

            ShowRelease showA, showB;
            boolean isFound = false;

            for (int i = 0; i < newCursor.getCount(); i++) {
                if (oldCursor.moveToPosition(i) && newCursor.moveToPosition(i)) {
                    showA = Contract.ShowReleaseTable.getShowInfo(oldCursor);
                    showB = Contract.ShowReleaseTable.getShowInfo(newCursor);
                    if (showA.getNumber() != showB.getNumber()) {
                        isFound = true;
                        animateRemoval(newCursor, i);
                        break;
                    }
                }
            }

            //значит это был последний элемент
            if (!isFound) {
                animateRemoval(newCursor, oldCursor.getCount() - 1);
            }
        } else {
            mRecyclerAdapter.swapCursor(newCursor);
        }
    }

    private void animateRemoval(Cursor newCursor, int position) {
        mRecyclerAdapter.swapWithRemoveAnimationForOnePosition(newCursor, position);
    }

    private void hideEmptyView() {
        mEmptyView.setVisibility(View.GONE);
    }

    private void showEmptyView(String message) {
        mEmptyView.setText(message);
        mEmptyView.setVisibility(View.VISIBLE);
    }

    abstract protected String getEmptyMessage();

    @Override
    public void filterData(String newText) {
        mCurFilter = !TextUtils.isEmpty(newText) ? newText : null;
        getLoaderManager().restartLoader(0, null, this);
    }

    @Override
    public String getFilter() {
        return mCurFilter;
    }
}
