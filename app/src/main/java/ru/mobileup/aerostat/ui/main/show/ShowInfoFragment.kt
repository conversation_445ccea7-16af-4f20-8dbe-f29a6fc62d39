package ru.mobileup.aerostat.ui.main.show

import android.database.Cursor
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.ScrollView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.loader.app.LoaderManager
import androidx.loader.content.CursorLoader
import androidx.loader.content.Loader
import ru.mobileup.aerostat.R
import ru.mobileup.aerostat.api.model.ShowRelease
import ru.mobileup.aerostat.storage.AerostatPrefs
import ru.mobileup.aerostat.storage.provider.Contract
import ru.mobileup.aerostat.ui.common.BaseFragment
import ru.mobileup.aerostat.ui.common.CurrentShowChangesListener
import ru.mobileup.aerostat.ui.common.bundleProperty
import ru.mobileup.aerostat.ui.common.showcase.AppShowcaseConfig
import ru.mobileup.aerostat.ui.common.showcase.AppShowcaseView
import ru.mobileup.aerostat.ui.common.showcase.MaterialShowcaseRectangleRoundCornersShape
import ru.mobileup.aerostat.ui.main.MainActivity
import ru.mobileup.aerostat.ui.main.ToolbarModeSwitcher
import ru.mobileup.aerostat.util.Logger
import ru.mobileup.aerostat.util.ShowReleaseUtils
import uk.co.deanwild.materialshowcaseview.MaterialShowcaseView
import uk.co.deanwild.materialshowcaseview.target.ViewTarget
import kotlin.math.abs

class ShowInfoFragment
    : BaseFragment(),
    LoaderManager.LoaderCallbacks<Cursor>,
    CurrentShowChangesListener {

    companion object {
        private const val TAG = "ShowInfoFragment"

        @JvmStatic
        fun getNewInstance(showRelease: ShowRelease, isCurrentPlayingShow: Boolean) =
            ShowInfoFragment().apply {
                this.showRelease = showRelease
                this.isPlayingCurrentShow = isCurrentPlayingShow
            }
    }

    private lateinit var showDescriptionText: TextView
    private lateinit var playlistButton: View
    private lateinit var playButton: View
    private lateinit var scrollView: ScrollView

    var showRelease: ShowRelease by bundleProperty()
    private var isPlayingCurrentShow: Boolean by bundleProperty()

    private val deltaScrollForShowFloating by lazy {
        resources.getDimensionPixelOffset(R.dimen.delta_scroll_for_floating)
    }

    private var isPlayButtonShowing = true
    private var isPlaylistShowing = false
    private var isShowcaseShowing = false

    override fun getTitle() = showRelease.number.toString() + " — " + showRelease.name

    override fun getSubtitle() = showRelease.subTitle.orEmpty()

    override fun getToolbarMode(): Int {
        return if (isPlaylistShowing) {
            ToolbarModeSwitcher.TOOLBAR_MODE_PLAYLIST
        } else {
            ToolbarModeSwitcher.TOOLBAR_MODE_SHOW
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_show_info, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        showDescriptionText = view.findViewById(R.id.showDescriptionText)
        playlistButton = view.findViewById(R.id.playlistButton)
        playButton = view.findViewById(R.id.playButton)
        scrollView = view.findViewById(R.id.scrollView)

        showDescriptionText.text = showRelease.description

        var previousScrollY = 0
        scrollView.viewTreeObserver.addOnScrollChangedListener {
            // skip if playing current show
            if (isPlayingCurrentShow || !isVisible) return@addOnScrollChangedListener

            val deltaY = scrollView.scrollY - previousScrollY

            if (abs(deltaY) > deltaScrollForShowFloating) {
                setShowPlayButton(doShow = deltaY < 0)
            }

            previousScrollY = scrollView.scrollY
        }

        playlistButton.setOnClickListener {
            val showPlayList = ShowPlayListFragment.getNewInstance(showRelease, true)
            requireActivity().supportFragmentManager.beginTransaction()
                .replace(R.id.container, showPlayList)
                .addToBackStack(null)
                .commit()

        }

        playButton.isVisible = isPlayingCurrentShow.not()
        playButton.setOnClickListener {
            MainActivity.playShowBroadcastMessage(
                activity,
                showRelease
            )
        }

        showcase()
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        //Создаем loader для связи состояний (избранное, загружен) с меню в экшнбаре
        LoaderManager.getInstance(this).initLoader(0, null, this)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.action_remove_fav -> ShowReleaseUtils.setFavorite(showRelease, false, activity)
            R.id.action_add_fav -> ShowReleaseUtils.setFavorite(showRelease, true, activity)
            R.id.action_download -> ShowReleaseUtils.downloadShowOnDisk(showRelease, activity)
            R.id.action_cancel_loading -> ShowReleaseUtils.cancelLoadingDialog(
                showRelease,
                activity
            )

            R.id.action_delete -> ShowReleaseUtils.showRemoveDialog(showRelease, activity)
            else -> return super.onOptionsItemSelected(item)
        }
        return true
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        try {
            menu.findItem(R.id.action_remove_fav).isVisible = showRelease.isFavorite
            menu.findItem(R.id.action_add_fav).isVisible = !showRelease.isFavorite
            menu.findItem(R.id.action_download).isVisible =
                showRelease.savedStatus == ShowRelease.STATUS_NOT_SAVED
            menu.findItem(R.id.action_cancel_loading).isVisible =
                showRelease.savedStatus == ShowRelease.STATUS_DOWNLOADING
            menu.findItem(R.id.action_delete).isVisible =
                showRelease.savedStatus == ShowRelease.STATUS_SAVED
        } catch (e: NullPointerException) {
            Logger.e("ShowInfoFragment", "not available toolbar mode!")
        } catch (e: Throwable) {

        }
    }

    override fun onCreateLoader(id: Int, args: Bundle?): Loader<Cursor> {
        Logger.d(TAG, "onCreateLoader id = $id")
        return CursorLoader(
            requireActivity(),
            Contract.ShowReleaseTable.buildShowUri(showRelease.number),
            null,
            null,
            null,
            null
        )
    }

    override fun onLoadFinished(loader: Loader<Cursor>, data: Cursor) {
        Logger.d(TAG, "onLoadFinished")
        if (data.moveToFirst()) {
            showRelease = Contract.ShowReleaseTable.getShowInfo(data)
        }
        requireActivity().invalidateOptionsMenu()
    }

    override fun onLoaderReset(loader: Loader<Cursor>) {
        Logger.d(TAG, "onLoaderReset")
        requireActivity().invalidateOptionsMenu()
    }

    private fun setShowPlayButton(doShow: Boolean) {
        if (doShow) {
            if (isPlayButtonShowing) return

            playButton.animate().translationY(0f).duration = 250
            isPlayButtonShowing = true
        } else {
            if (isPlayButtonShowing.not()) return

            playButton.animate().translationY(500f).duration = 250
            isPlayButtonShowing = false
        }
    }

    override fun onCurrentShowChange(currentShowRelease: ShowRelease?) {
        isPlayingCurrentShow = showRelease.number == currentShowRelease?.number
        playButton.isVisible = isPlayingCurrentShow.not()
    }

    private fun showcase() {

        if (!AerostatPrefs.getShowcaseWasShown(context)) {

            MaterialShowcaseView.resetAll(requireContext())

            Log.d("!!", "init showcase")

            val config = AppShowcaseConfig().apply {
                renderOverNavigationBar = true
                maskColor = resources.getColor(R.color.showcase_background_color)
                gravity = Gravity.BOTTOM
                contentBottomMargin =
                    resources.getDimensionPixelSize(R.dimen.showcase_content_bottom_margin)
                contentTextAlpha = 1F
            }

            AppShowcaseView.Builder(requireActivity())
                .singleUse("playlist_showcase")
                .setDelay(500)
                .setTarget(playlistButton)
                .setTitleText(getString(R.string.showcase_title_text))
                .setContentText(getString(R.string.showcase_desc1_text))
                .setDismissText(getString(R.string.showcase_button_ok))
                .setShapePadding(resources.getDimensionPixelSize(R.dimen.showcase_target_padding))
                .build()
                .showWithConfig(config,
                    onDisplay = {
                        isShowcaseShowing = true
                    },
                    onDismiss = {
                        isShowcaseShowing = false
                        if (activity == null) return@showWithConfig

                        val showcaseShape = MaterialShowcaseRectangleRoundCornersShape(
                            bounds = ViewTarget((activity as? MainActivity)!!.playerTitleView).bounds,
                            paddingBottom = resources.getDimensionPixelSize(R.dimen.showcase_player_title_bottom_padding),
                            cornerRadius = resources.getDimension(R.dimen.showcase_player_corner_radius)
                        )

                        AppShowcaseView.Builder(requireActivity())
                            .setTarget((activity as? MainActivity)!!.playerTitleView)
                            .setTitleText(getString(R.string.showcase_title_text))
                            .setContentText(getString(R.string.showcase_desc2_text))
                            .setDismissText(getString(R.string.showcase_button_ok))
                            .setShape(showcaseShape)
                            .build()
                            .showWithConfig(config,
                                onDisplay = {
                                    isShowcaseShowing = true
                                },
                                onDismiss = {
                                    isShowcaseShowing = false
                                }
                            )
                    })

            AerostatPrefs.putShowcaseWasShown(context, true)
        }
    }
}