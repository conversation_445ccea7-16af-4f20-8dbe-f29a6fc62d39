package androidx.recyclerview.widget;

import android.annotation.SuppressLint;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;

@SuppressLint("VisibleForTests")
public class AppFastScroller extends FastScroller {

    private final int mMinVerticalThumbHeight;

    public AppFastScroller(
            RecyclerView recyclerView,
            StateListDrawable verticalThumbDrawable,
            Drawable verticalTrackDrawable,
            StateListDrawable horizontalThumbDrawable,
            Drawable horizontalTrackDrawable,
            int defaultWidth,
            int scrollbarMinimumRange,
            int margin,
            int minVerticalThumbHeight
    ) {
        super(recyclerView, verticalThumbDrawable, verticalTrackDrawable, horizontalThumbDrawable, horizontalTrackDrawable, defaultWidth, scrollbarMinimumRange, margin);
        mMinVerticalThumbHeight = minVerticalThumbHeight;
    }

    @Override
    void updateScrollPosition(int offsetX, int offsetY) {
        super.updateScrollPosition(offsetX, offsetY);
        if (mVerticalThumbHeight < mMinVerticalThumbHeight) {
            mVerticalThumbHeight = mMinVerticalThumbHeight;
        }
    }

}
