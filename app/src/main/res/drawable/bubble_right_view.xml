<?xml version="1.0" encoding="utf-8"?>
<layer-list
    xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape>
            <gradient android:startColor="#00ffffff"
                      android:centerY="2%"
                      android:centerColor="#44000000"
                      android:endColor="#44000000"
                      android:angle="90"
                />
            <corners android:bottomLeftRadius="12dp"
                     android:bottomRightRadius="0.1dip"
                     android:topLeftRadius="12dip"
                     android:topRightRadius="12dp"/>
            <padding android:top="0dp" android:right="1dp" android:bottom="2dp" android:left="0dp" />
        </shape>
    </item>
    <item>
        <shape>
            <solid android:color="@color/accent"/>
            <corners android:bottomLeftRadius="12dp"
                     android:bottomRightRadius="0.1dp"
                     android:topLeftRadius="12dip"
                     android:topRightRadius="12dp"/>
        </shape>
    </item>

</layer-list>