<?xml version="1.0" encoding="utf-8"?>
<layer-list
    xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape>
            <gradient android:startColor="#00ffffff"
                      android:endColor="#44000000"
                      android:angle="270"
                      />
            <corners android:bottomLeftRadius="0.1dp"
                     android:bottomRightRadius="12dip"
                     android:topLeftRadius="12dip"
                     android:topRightRadius="12dp"/>
            <padding android:top="1dp" android:right="1dp" android:bottom="2dp" android:left="1dp" />
        </shape>
    </item>
    <item>
        <shape>
            <solid android:color="@color/accent"/>
            <corners android:bottomLeftRadius="0.1dp"
                     android:bottomRightRadius="12dip"
                     android:topLeftRadius="12dip"
                     android:topRightRadius="12dp"/>
        </shape>
    </item>

</layer-list>