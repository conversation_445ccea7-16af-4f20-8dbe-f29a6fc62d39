<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content_box"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    tools:background="@color/showcase_background_color">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"
        android:paddingLeft="5dp"
        android:textSize="24sp"
        android:layout_marginBottom="15dp"
        tools:text="Заголовок"/>

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"
        android:paddingLeft="5dp"
        android:textSize="16sp"
        tools:text="Какой-то текст длинный длинный текст"
        android:layout_marginTop="24dp"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/tv_dismiss"
        style="@style/SolidButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:paddingBottom="10dp"
        android:paddingTop="10dp"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:textColor="@android:color/white"
        android:textSize="18sp"
        android:visibility="gone"
        tools:visibility="visible"
        tools:text="OK"/>

    <TextView
        android:id="@+id/tv_skip"
        android:background="?android:attr/selectableItemBackground"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:clickable="true"
        android:paddingBottom="10dp"
        android:paddingTop="10dp"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:textColor="@android:color/white"
        android:textSize="18dp"
        android:visibility="visible" />

</LinearLayout>