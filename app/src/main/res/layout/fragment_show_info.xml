<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true">

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/showDescriptionText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:textIsSelectable="true"
            android:textSize="16sp" />

    </ScrollView>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/playlistButton"
        style="@style/PlayListFloatingButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:layout_margin="16dp"
        app:srcCompat="@drawable/ic_playlist" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/playButton"
        style="@style/PlayFloatingButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_margin="16dp"
        android:tint="@android:color/white"
        android:visibility="gone"
        app:maxImageSize="36dp"
        app:srcCompat="@drawable/ic_play_arrow"
        tools:visibility="visible" />

</RelativeLayout>
