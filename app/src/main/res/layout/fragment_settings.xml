<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/force_choose_download_path_warning"
        android:paddingHorizontal="16dp"
        android:paddingTop="16dp"
        android:paddingBottom="4dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/settings_force_choose_download_path_warning"
        android:textColor="@color/accent"
        android:textStyle="bold"
        android:textSize="16sp"/>

    <LinearLayout
        android:id="@+id/settings_download_path"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/item_list_bg"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/settings_download_path_label"
            android:textSize="16sp"/>

        <TextView
            android:id="@+id/downloadPathText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="start"
            android:singleLine="true"
            android:textColor="@color/secondary_text"
            tools:text="/some/directory/for/downloads"/>

    </LinearLayout>
</LinearLayout>
