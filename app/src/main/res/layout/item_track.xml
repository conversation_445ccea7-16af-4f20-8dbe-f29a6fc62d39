<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="16dp"
    android:paddingTop="12dp"
    android:paddingEnd="16dp"
    android:paddingBottom="12dp"
    android:background="?selectableItemBackground">

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="?android:textColorPrimary"
        android:textSize="16sp"
        android:layout_marginRight="16dp"
        app:layout_constraintEnd_toStartOf="@+id/yandexMusicIcon"
        app:layout_constraintBottom_toTopOf="@+id/subtitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Jeff Lynne's ELO" />

    <TextView
        android:id="@+id/subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="?android:textColorSecondary"
        android:textSize="16sp"
        android:singleLine="true"
        android:ellipsize="end"
        android:layout_marginRight="16dp"
        app:layout_constraintEnd_toStartOf="@+id/yandexMusicIcon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        tools:text="10538 Overture (40th Anniversary kjnasiudhafd)" />

    <TextView
        android:id="@+id/yandexMusicIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/listen_on_ym_text"
        android:textSize="12sp"
        android:textColor="?android:textColorSecondary"
        android:drawablePadding="5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:drawableStartCompat="@drawable/ic_yandex_music" />

</androidx.constraintlayout.widget.ConstraintLayout>