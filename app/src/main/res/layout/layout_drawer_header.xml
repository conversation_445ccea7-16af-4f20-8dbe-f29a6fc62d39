<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?colorAccent"
    android:paddingStart="16dp"
    android:paddingTop="40dp"
    android:paddingEnd="16dp"
    android:paddingBottom="24dp">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:src="@drawable/ic_launcher"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/drawer_menu_header_title"
        android:textColor="@android:color/white"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/icon"/>

</androidx.constraintlayout.widget.ConstraintLayout>