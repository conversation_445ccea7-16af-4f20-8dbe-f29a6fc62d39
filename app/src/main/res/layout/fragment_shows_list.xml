<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/fr_list_swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ru.mobileup.aerostat.ui.common.AppRecyclerView
            android:id="@+id/fr_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/item_bg"
            android:scrollbars="vertical"
            app:fastScrollEnabled="true"
            app:fastScrollHorizontalThumbDrawable="@drawable/bg_list_thumb_selector"
            app:fastScrollHorizontalTrackDrawable="@drawable/bg_line_selector"
            app:fastScrollVerticalThumbDrawable="@drawable/bg_list_thumb_selector"
            app:fastScrollVerticalTrackDrawable="@drawable/bg_line_selector"
            tools:listitem="@layout/item_show_release"/>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <ImageView
        android:id="@+id/network_error_icon"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_above="@+id/network_error_text"
        android:layout_centerHorizontal="true"
        android:visibility="gone"
        app:srcCompat="@drawable/ic_signal_wifi_off"
        tools:ignore="ContentDescription"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/network_error_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_margin="16dp"
        android:gravity="center"
        android:text="@string/error_network_stub_msg"
        android:textColor="@color/secondary_text"
        android:textSize="20sp"
        android:visibility="gone"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/empty_list_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_margin="16dp"
        android:gravity="center"
        android:text="@string/empty_list_msg"
        android:textColor="@color/primary_text"
        android:textSize="20sp"
        android:visibility="gone"/>

</RelativeLayout>
