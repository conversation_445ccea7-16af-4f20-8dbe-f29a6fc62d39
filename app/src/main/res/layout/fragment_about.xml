<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentTop="true"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/about_logo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_about_logo" />

            <TextView
                android:id="@+id/about_company_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                android:textSize="32sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="16dp"
                android:text="@string/about_text"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/about_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingTop="16dp"
                android:textSize="14sp"
                tools:text="Версия: 1.2.3" />

            <TextView
                android:id="@+id/about_textbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:autoLink="email"
                android:gravity="center"
                android:padding="16dp"
                android:text="@string/about_subtext"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:autoLink="web"
                android:gravity="center"
                android:padding="16dp"
                android:text="@string/about_thanks" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

        </LinearLayout>
    </ScrollView>

    <ImageView
        android:id="@+id/about_face"
        android:layout_width="140dp"
        android:layout_height="150dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:scaleType="fitXY"
        android:src="@drawable/face"
        android:visibility="gone" />

</RelativeLayout>
