<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <!-- The main content view -->
    <LinearLayout
        android:id="@+id/main_root_lay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:orientation="vertical">

        <include layout="@layout/view_toolbar"/>

        <FrameLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="-10dp"
            android:layout_weight="1"
            tools:background="@color/black_translucent_20" />

        <include
            layout="@layout/view_player_manipulation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="0"/>

    </LinearLayout>

    <!-- The navigation drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/drawer_navigation"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/layout_drawer_header"
        app:itemBackground="@drawable/bg_drawer_menu_item"
        app:itemIconTint="@drawable/bg_navigation_icon_color_selector"
        app:itemTextColor="@drawable/bg_navigation_text_color_selector"
        app:menu="@menu/drawer_menu"/>
</androidx.drawerlayout.widget.DrawerLayout>