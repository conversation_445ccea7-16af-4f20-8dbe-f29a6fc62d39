<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?selectableItemBackground">

    <ProgressBar
        android:id="@+id/item_show_progress"
        style="@android:style/Widget.ProgressBar.Horizontal"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginVertical="14dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:progressDrawable="@drawable/bg_circle_progress"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:progress="30"/>

    <TextView
        android:id="@+id/item_show_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="@color/icons"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/item_show_progress"
        app:layout_constraintEnd_toEndOf="@+id/item_show_progress"
        app:layout_constraintStart_toStartOf="@+id/item_show_progress"
        app:layout_constraintTop_toTopOf="@+id/item_show_progress"
        tools:text="555"/>

    <TextView
        android:id="@+id/item_show_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:singleLine="true"
        android:textColor="@color/primary_text"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@+id/item_show_subtitle"
        app:layout_constraintEnd_toStartOf="@+id/item_show_fav"
        app:layout_constraintStart_toEndOf="@+id/item_show_progress"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="14dp"
        tools:text="dsfsf"/>

    <ImageView
        android:id="@+id/item_show_subtitle_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="4dp"
        android:paddingEnd="4dp"
        android:src="@drawable/tv_downloading"
        app:layout_constraintBottom_toBottomOf="@+id/item_show_subtitle"
        app:layout_constraintStart_toStartOf="@+id/item_show_title"
        app:layout_constraintTop_toTopOf="@+id/item_show_subtitle"/>

    <TextView
        android:id="@+id/item_show_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="@color/secondary_text"
        android:textSize="14sp"
        android:layout_marginBottom="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/item_show_title"
        app:layout_constraintStart_toEndOf="@+id/item_show_subtitle_status"
        app:layout_constraintTop_toBottomOf="@+id/item_show_title"
        tools:text="dsfsf"/>

    <ImageView
        android:id="@+id/item_show_fav"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:paddingHorizontal="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_star_outline"
        app:tint="@color/primary_dark"/>

</androidx.constraintlayout.widget.ConstraintLayout>