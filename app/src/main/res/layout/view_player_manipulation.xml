<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/player_manipulation_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true">

    <LinearLayout
        android:id="@+id/main_play_data_lay"
        android:layout_width="match_parent"
        android:layout_height="@dimen/player_lay_height_without_seekbar"
        android:layout_below="@id/player_manipulation_title"
        android:layout_centerHorizontal="true"
        android:background="@android:color/white"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingLeft="16dp"
        android:paddingRight="16dp">

        <TextView
            android:id="@+id/player_manipulation_current_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/primary_text"
            android:textSize="16sp"
            tools:text="17:27" />

        <ImageView
            android:id="@+id/player_manipulation_rewind"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="8dp"
            android:layout_weight="1"
            android:scaleType="centerInside"
            android:src="@drawable/ic_notif_rewind" />

        <ImageView
            android:id="@+id/player_manipulation_play"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_player_manipulation_play" />

        <ImageView
            android:id="@+id/player_manipulation_forward"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginRight="8dp"
            android:layout_weight="1"
            android:scaleType="centerInside"
            android:src="@drawable/ic_notif_forward" />

        <TextView
            android:id="@+id/player_manipulation_full_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/primary_text"
            android:textSize="16sp"
            tools:text="44:27" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:layout_marginTop="8dp"
        android:background="@android:color/white"/>

    <TextView
        android:id="@+id/player_manipulation_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/manipulation_player_title_height"
        android:layout_marginTop="@dimen/margin_between_seekbar"
        android:ellipsize="marquee"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:paddingTop="2dp"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:textColor="@color/secondary_text"
        android:textSize="16sp"
        android:layout_marginBottom="-8dp"
        android:layout_centerHorizontal="true"
        android:background="?selectableItemBackground"
        tools:text="The animals — The house of the rising sun" />

    <SeekBar
        android:id="@+id/player_manipulation_seek"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:paddingBottom="8dp"
        android:paddingLeft="0dp"
        android:paddingRight="0dp"
        android:background="@null"
        android:thumb="@drawable/ic_seek_thumb"
        android:thumbOffset="6dp"
        tools:progress="40"/>

    <ProgressBar
        android:id="@+id/player_manipulation_prog"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/player_lay_height"
        android:background="@android:color/white"
        android:padding="24dp"
        tools:visibility="gone" />
</RelativeLayout>