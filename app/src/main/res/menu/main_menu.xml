<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context="ru.mobileup.aerostat.ui.main.MainActivity">

    <item android:id="@+id/action_search"
        android:title="@string/main_menu_action_search_title"
        android:icon="@drawable/ic_search"
        app:showAsAction="ifRoom|collapseActionView"
        app:actionViewClass="androidx.appcompat.widget.SearchView" />

    <item
        android:id="@+id/action_sort"
        android:title="@string/main_menu_action_sort_title"
        android:icon="@drawable/ic_sort"
        app:showAsAction="ifRoom"/>

</menu>