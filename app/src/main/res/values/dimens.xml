<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="def_item_height">72dp</dimen>
    <dimen name="player_lay_height">112dp</dimen>

    <dimen name="margin_between_seekbar">20dp</dimen>

    <!-- player_lay_height - 10dp -->
    <dimen name="player_lay_height_without_seekbar">73dp</dimen>

    <dimen name="manipulation_player_title_height">32dp</dimen>

    <!-- player_lay_height_without_seekbar + manipulation_player_title_height -->
    <dimen name="manipulation_player_lay_height_without_seekbar">105dp</dimen>

    <!-- player_lay_height + 16dp -->
    <dimen name="player_lay_height_with_padding">93dp</dimen>
    <dimen name="list_divider_height">1dp</dimen>

    <dimen name="delta_scroll_for_floating">3dp</dimen>
    
    <dimen name="offset_Y_bubble_seek_bar">12dp</dimen>

    <dimen name="fab_margin">16dp</dimen>
    
    <dimen name="fast_scroll_min_thumb_height">16dp</dimen>

    <dimen name="showcase_target_padding">38dp</dimen>
    <dimen name="showcase_content_bottom_margin">100dp</dimen>
    <dimen name="showcase_player_title_bottom_padding">8dp</dimen>
    <dimen name="showcase_player_corner_radius">24dp</dimen>

</resources>
