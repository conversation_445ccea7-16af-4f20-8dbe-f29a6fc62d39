<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">

        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/accent</item>

        <item name="android:statusBarColor">@color/black_translucent_20</item>
        <item name="android:windowBackground">@android:color/white</item>

        <item name="android:textColor">@color/primary_text</item>
        <item name="android:textColorSecondary">@color/black_transparent_54</item>

        <item name="android:windowAnimationStyle">@null</item>

        <item name="windowActionModeOverlay">true</item>

        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>

        <item name="android:listDivider">@drawable/bg_list_divider</item>

        <item name="buttonBarPositiveButtonStyle">@style/AlertButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/AlertButtonStyle</item>
        <item name="android:buttonBarPositiveButtonStyle">@style/AlertButtonStyle</item>
        <item name="android:buttonBarNegativeButtonStyle">@style/AlertButtonStyle</item>
    </style>
    
    <style name="AlertButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">?colorAccent</item>
        <item name="backgroundTint">@android:color/white</item>
    </style>

    <style name="SolidButton" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/accent</item>
        <item name="android:textColor">@android:color/white</item>
    </style>

    <style name="PlayFloatingButton" parent="Widget.MaterialComponents.FloatingActionButton">
        <item name="backgroundTint">@color/accent</item>
        <item name="tint">@android:color/white</item>
    </style>

    <style name="PlayListFloatingButton" parent="Widget.MaterialComponents.FloatingActionButton">
        <item name="backgroundTint">@android:color/white</item>
        <item name="tint">@color/accent</item>

    </style>

</resources>
