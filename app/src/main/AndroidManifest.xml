<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="ru.mobileup.aerostat">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK"/>

    <!-- Эти пермишены оставлены для миграции, но в коде они не запрашиваются -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <application
        android:allowBackup="false"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/AppTheme"
        android:name=".AerostatApplication"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="n">

        <activity
            android:exported="true"
            android:screenOrientation="portrait"
            android:name=".ui.main.MainActivity"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustNothing"
            tools:ignore="LockedOrientationActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:screenOrientation="portrait"
            android:name=".ui.about.AboutActivity"
            android:label="@string/about"
            tools:ignore="LockedOrientationActivity" />

        <activity
            android:screenOrientation="portrait"
            android:name=".ui.settings.SettingsActivity"
            android:label="@string/settings"
            tools:ignore="LockedOrientationActivity" />

        <service
            android:name="dmdevgo.hunky.core.HunkyService"
            android:exported="false"/>

        <provider
            android:name=".storage.provider.AerostatContentProvider"
            android:authorities="ru.mobileup.aerostat.storage.provider.AerostatContentProvider"
            android:exported="false"/>

        <service android:name=".player.PodcastPlayerService"
            android:foregroundServiceType="mediaPlayback"/>

    </application>

</manifest>
