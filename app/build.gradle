apply plugin: 'com.android.application'
apply plugin: 'kotlinx-serialization'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.google.firebase.crashlytics'

repositories {
    flatDir {
        dirs 'libs'
    }
    google()
    mavenCentral()
    maven {
        url "https://jitpack.io"
    }
}

//CI
apply from: '../ci.gradle'

android {
    compileSdk 35

    namespace "ru.mobileup.aerostat"

    defaultConfig {
        applicationId "ru.mobileup.aerostat"
        minSdkVersion 21
        targetSdkVersion 35
        versionName "2.13.1"

        vectorDrawables.useSupportLibrary = true
        multiDexEnabled true

        renderscriptTargetApi 33
        renderscriptSupportModeEnabled true
    }

    lintOptions {
        abortOnError false
    }

    signingConfigs {
        debug {
            storeFile file("aerostat-debug.keystore")
            storePassword = "android"
            keyAlias = "androiddebugkey"
            keyPassword = "android"
        }

        release {
            storeFile file("aerostat.keystore")
            storePassword = "aerostat15"
            keyAlias = "aerostat"
            keyPassword = "aerostat15"
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }

        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8.toString()
    }

    buildFeatures {
        buildConfig true
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation "androidx.appcompat:appcompat:1.7.1"
    implementation 'com.google.android.material:material:1.12.0'
    implementation "androidx.recyclerview:recyclerview:1.4.0"
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"

    implementation 'androidx.core:core-ktx:1.16.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'

    implementation 'org.jsoup:jsoup:1.8.1'
    implementation 'com.google.android.gms:play-services-analytics:18.1.0'
    implementation 'com.github.hotchemi:android-rate:0.4.3'
    implementation 'com.google.firebase:firebase-core:21.1.1'
    implementation 'com.google.firebase:firebase-database:21.0.0'
    implementation 'com.android.support:multidex:1.0.3'
    implementation "androidx.activity:activity:1.10.1"

    // used libs/fetch2-3.2.2.aar from https://github.com/Kutikov/fetch2-android
    // implementation 'androidx.tonyodev.fetch2:xfetch2:3.1.6'

    // ExoPlayer
    implementation 'com.google.android.exoplayer:exoplayer:2.14.0'

    implementation 'com.github.deano2390:MaterialShowcaseView:1.3.4'
    implementation 'com.github.Dimezis:BlurView:version-1.6.3'

    implementation "org.jetbrains.kotlinx:kotlinx-serialization-json:1.4.1"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"

    implementation 'com.github.kittinunf.fuel:fuel:2.2.1'
    implementation 'com.github.kittinunf.fuel:fuel-coroutines:2.2.1'

    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

    // Google Analytics
    implementation 'com.google.firebase:firebase-analytics:22.0.2'

    // Firebase Crashlytics
    implementation 'com.google.firebase:firebase-crashlytics:19.0.3'

    // Room
    implementation "androidx.room:room-runtime:2.6.1"
    kapt "androidx.room:room-compiler:2.6.1"
}

apply plugin: 'com.google.gms.google-services'
