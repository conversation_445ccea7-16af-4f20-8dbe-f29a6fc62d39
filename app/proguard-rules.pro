# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /home/<USER>/WORK/sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

-keep class !android.support.v7.internal.view.menu.**,android.support.** {*;}
-keep class org.jsoup.** { *; }

-keep class com.turhanoz.** { *; }
-dontwarn sun.misc.Unsafe

-keepattributes *Annotation*
-keepattributes Signature
-dontwarn com.squareup.**
-keep class com.squareup.** { *; }
-dontwarn okio.**
-keep class okio.** { *; }

-keep class com.parse.** { *; }